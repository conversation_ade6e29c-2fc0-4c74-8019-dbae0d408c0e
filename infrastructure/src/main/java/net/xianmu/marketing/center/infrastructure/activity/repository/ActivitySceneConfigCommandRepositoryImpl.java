package net.xianmu.marketing.center.infrastructure.activity.repository;


import net.xianmu.marketing.center.domain.activity.repository.ActivitySceneConfigCommandRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySceneConfigCommandParam;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivitySceneConfigConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySceneConfigMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-06-10 15:41:35
* @version 1.0
*
*/
@Repository
public class ActivitySceneConfigCommandRepositoryImpl implements ActivitySceneConfigCommandRepository {

    @Autowired
    private ActivitySceneConfigMapper activitySceneConfigMapper;
    @Override
    public ActivitySceneConfigEntity insertSelective(ActivitySceneConfigCommandParam param) {
        ActivitySceneConfig activitySceneConfig = ActivitySceneConfigConverter.toActivitySceneConfig(param);
        activitySceneConfigMapper.insertSelective(activitySceneConfig);
        return ActivitySceneConfigConverter.toActivitySceneConfigEntity(activitySceneConfig);
    }

    @Override
    public int updateSelectiveById(ActivitySceneConfigCommandParam param){
        return activitySceneConfigMapper.updateByPrimaryKeySelective(ActivitySceneConfigConverter.toActivitySceneConfig(param));
    }

    @Override
    public void updateDelFlag(Long basicInfoId) {
        activitySceneConfigMapper.updateDelFlag(basicInfoId);
    }
}