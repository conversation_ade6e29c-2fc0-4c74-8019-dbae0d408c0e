package net.xianmu.marketing.center.infrastructure.activity.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityBasicInfoCommandParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo;

/**
 * @author: <EMAIL>
 * @create: 2023/12/27
 */
public class ActivityBasicInfoConverter {


    private ActivityBasicInfoConverter() {
        // 无需实现
    }

    public static List<ActivityBasicInfoEntity> toActivityBasicInfoEntityList(List<ActivityBasicInfo> activityBasicInfoList) {
        if (activityBasicInfoList == null) {
            return Collections.emptyList();
        }
        List<ActivityBasicInfoEntity> activityBasicInfoEntityList = new ArrayList<>();
        for (ActivityBasicInfo activityBasicInfo : activityBasicInfoList) {
            activityBasicInfoEntityList.add(toActivityBasicInfoEntity(activityBasicInfo));
        }
        return activityBasicInfoEntityList;
    }

    public static ActivityBasicInfoEntity toActivityBasicInfoEntity(ActivityBasicInfo activityBasicInfo) {
        if (activityBasicInfo == null) {
            return null;
        }
        ActivityBasicInfoEntity activityBasicInfoEntity = new ActivityBasicInfoEntity();
        activityBasicInfoEntity.setId(activityBasicInfo.getId());
        activityBasicInfoEntity.setName(activityBasicInfo.getName());
        activityBasicInfoEntity.setStartTime(activityBasicInfo.getStartTime());
        activityBasicInfoEntity.setEndTime(activityBasicInfo.getEndTime());
        activityBasicInfoEntity.setIsPermanent(activityBasicInfo.getIsPermanent());
        activityBasicInfoEntity.setStatus(activityBasicInfo.getStatus());
        activityBasicInfoEntity.setNeedPre(activityBasicInfo.getNeedPre());
        activityBasicInfoEntity.setPreStartTime(activityBasicInfo.getPreStartTime());
        activityBasicInfoEntity.setPreEndTime(activityBasicInfo.getPreEndTime());
        activityBasicInfoEntity.setType(activityBasicInfo.getType());
        activityBasicInfoEntity.setTag(activityBasicInfo.getTag());
        activityBasicInfoEntity.setRemark(activityBasicInfo.getRemark());
        activityBasicInfoEntity.setCreatorId(activityBasicInfo.getCreatorId());
        activityBasicInfoEntity.setUpdaterId(activityBasicInfo.getUpdaterId());
        activityBasicInfoEntity.setDelFlag(activityBasicInfo.getDelFlag());
        activityBasicInfoEntity.setCreateTime(activityBasicInfo.getCreateTime());
        activityBasicInfoEntity.setUpdateTime(activityBasicInfo.getUpdateTime());
        activityBasicInfoEntity.setOwnerId(activityBasicInfo.getOwnerId());
        activityBasicInfoEntity.setSystemOrigin(activityBasicInfo.getSystemOrigin());
        activityBasicInfoEntity.setAuditStatus(activityBasicInfo.getAuditStatus());
        activityBasicInfoEntity.setAuditUserId(activityBasicInfo.getAuditUserId());
        activityBasicInfoEntity.setAuditRemark(activityBasicInfo.getAuditRemark());
        activityBasicInfoEntity.setAuditTime(activityBasicInfo.getAuditTime());
        return activityBasicInfoEntity;
    }

    public static ActivityBasicInfo toActivityBasicInfo(ActivityBasicInfoCommandParam param) {
        if (param == null) {
            return null;
        }
        ActivityBasicInfo activityBasicInfo = new ActivityBasicInfo();
        activityBasicInfo.setId(param.getId());
        activityBasicInfo.setName(param.getName());
        activityBasicInfo.setStartTime(param.getStartTime());
        activityBasicInfo.setEndTime(param.getEndTime());
        activityBasicInfo.setIsPermanent(param.getIsPermanent());
        activityBasicInfo.setStatus(param.getStatus());
        activityBasicInfo.setNeedPre(param.getNeedPre());
        activityBasicInfo.setPreStartTime(param.getPreStartTime());
        activityBasicInfo.setPreEndTime(param.getPreEndTime());
        activityBasicInfo.setType(param.getType());
        activityBasicInfo.setTag(param.getTag());
        activityBasicInfo.setRemark(param.getRemark());
        activityBasicInfo.setCreatorId(param.getCreatorId());
        activityBasicInfo.setUpdaterId(param.getUpdaterId());
        activityBasicInfo.setDelFlag(param.getDelFlag());
        activityBasicInfo.setCreateTime(param.getCreateTime());
        activityBasicInfo.setUpdateTime(param.getUpdateTime());
        activityBasicInfo.setOwnerId(param.getOwnerId());
        activityBasicInfo.setSystemOrigin(param.getSystemOrigin());
        activityBasicInfo.setAuditStatus(param.getAuditStatus());
        activityBasicInfo.setAuditUserId(param.getAuditUserId());
        activityBasicInfo.setAuditRemark(param.getAuditRemark());
        activityBasicInfo.setAuditTime(param.getAuditTime());
        return activityBasicInfo;
    }
}
