package net.xianmu.marketing.center.infrastructure.activity.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuPriceEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuPriceQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuPriceQueryRepository;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivitySkuPriceConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySkuPriceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivitySkuPriceQueryRepositoryImpl implements ActivitySkuPriceQueryRepository {

    @Autowired
    private ActivitySkuPriceMapper activitySkuPriceMapper;


    @Override
    public PageInfo<ActivitySkuPriceEntity> getPage(ActivitySkuPriceQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivitySkuPriceEntity> entities = activitySkuPriceMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivitySkuPriceEntity selectById(Long id) {
        return ActivitySkuPriceConverter.toActivitySkuPriceEntity(activitySkuPriceMapper.selectByPrimaryKey(id));
    }


    @Override
    public List<ActivitySkuPriceEntity> selectByCondition(ActivitySkuPriceQueryParam param) {
        return ActivitySkuPriceConverter.toActivitySkuPriceEntityList(activitySkuPriceMapper.selectByCondition(param));
    }
}
