package net.xianmu.marketing.center.infrastructure.activity.repository;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.repository.ActivityBasicInfoCommandRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityBasicInfoCommandParam;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivityBasicInfoConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityBasicInfoMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivityBasicInfoCommandRepositoryImpl implements ActivityBasicInfoCommandRepository {

    @Autowired
    private ActivityBasicInfoMapper activityBasicInfoMapper;

    @Override
    public ActivityBasicInfoEntity insertSelective(ActivityBasicInfoCommandParam param) {
        ActivityBasicInfo activityBasicInfo = ActivityBasicInfoConverter.toActivityBasicInfo(param);
        activityBasicInfoMapper.insertSelective(activityBasicInfo);
        return ActivityBasicInfoConverter.toActivityBasicInfoEntity(activityBasicInfo);
    }

    @Override
    public int updateSelectiveById(ActivityBasicInfoCommandParam param){
        return activityBasicInfoMapper.updateByPrimaryKeySelective(ActivityBasicInfoConverter.toActivityBasicInfo(param));
    }
}
