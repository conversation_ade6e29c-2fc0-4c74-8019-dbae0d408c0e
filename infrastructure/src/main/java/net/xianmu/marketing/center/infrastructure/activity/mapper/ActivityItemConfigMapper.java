package net.xianmu.marketing.center.infrastructure.activity.mapper;

import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityItemConfigQueryParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityItemConfigMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivityItemConfig record);

    
    int insertSelective(ActivityItemConfig record);

    
    ActivityItemConfig selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivityItemConfig record);

    
    int updateByPrimaryKey(ActivityItemConfig record);

    int updateDelFlag(ActivityItemConfig itemConfig);

    ActivityItemConfigEntity getByInfoId(@Param("basicInfoId") Long basicInfoId);

    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<ActivityItemConfig> selectByCondition(ActivityItemConfigQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<ActivityItemConfigEntity> getPage(ActivityItemConfigQueryParam param);

}