package net.xianmu.marketing.center.infrastructure.activity.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityItemConfigCommandParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig;

/**
 * @author: <EMAIL>
 * @create: 2023/12/27
 */
public class ActivityItemConfigConverter {


    private ActivityItemConfigConverter() {
        // 无需实现
    }

    public static List<ActivityItemConfigEntity> toActivityItemConfigEntityList(List<ActivityItemConfig> activityItemConfigList) {
        if (activityItemConfigList == null) {
            return Collections.emptyList();
        }
        List<ActivityItemConfigEntity> activityItemConfigEntityList = new ArrayList<>();
        for (ActivityItemConfig activityItemConfig : activityItemConfigList) {
            activityItemConfigEntityList.add(toActivityItemConfigEntity(activityItemConfig));
        }
        return activityItemConfigEntityList;
    }

    public static ActivityItemConfigEntity toActivityItemConfigEntity(ActivityItemConfig activityItemConfig) {
        if (activityItemConfig == null) {
            return null;
        }
        ActivityItemConfigEntity activityItemConfigEntity = new ActivityItemConfigEntity();
        activityItemConfigEntity.setId(activityItemConfig.getId());
        activityItemConfigEntity.setBasicInfoId(activityItemConfig.getBasicInfoId());
        activityItemConfigEntity.setGoodSelectWay(activityItemConfig.getGoodSelectWay());
        activityItemConfigEntity.setPricingType(activityItemConfig.getPricingType());
        activityItemConfigEntity.setPricingTypeExt(activityItemConfig.getPricingTypeExt());
        activityItemConfigEntity.setDiscountPercentage(activityItemConfig.getDiscountPercentage());
        activityItemConfigEntity.setDiscount(activityItemConfig.getDiscount());
        activityItemConfigEntity.setUpdaterId(activityItemConfig.getUpdaterId());
        activityItemConfigEntity.setDelFlag(activityItemConfig.getDelFlag());
// Not mapped FROM fields:
// createTime
// updateTime
        return activityItemConfigEntity;
    }

    public static ActivityItemConfig toActivityItemConfig(ActivityItemConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        ActivityItemConfig activityItemConfig = new ActivityItemConfig();
        activityItemConfig.setId(param.getId());
        activityItemConfig.setBasicInfoId(param.getBasicInfoId());
        activityItemConfig.setGoodSelectWay(param.getGoodSelectWay());
        activityItemConfig.setPricingType(param.getPricingType());
        activityItemConfig.setPricingTypeExt(param.getPricingTypeExt());
        activityItemConfig.setDiscountPercentage(param.getDiscountPercentage());
        activityItemConfig.setDiscount(param.getDiscount());
        activityItemConfig.setUpdaterId(param.getUpdaterId());
        activityItemConfig.setDelFlag(param.getDelFlag());
        activityItemConfig.setCreateTime(param.getCreateTime());
        activityItemConfig.setUpdateTime(param.getUpdateTime());
        return activityItemConfig;
    }
}
