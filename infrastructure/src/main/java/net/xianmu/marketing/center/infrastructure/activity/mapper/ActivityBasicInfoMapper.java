package net.xianmu.marketing.center.infrastructure.activity.mapper;

import java.util.List;

import net.xianmu.marketing.center.domain.activity.dto.ActivityPageRespDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemScopeEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivityPageEntity;
import net.xianmu.marketing.center.domain.activity.param.ActivityBasicQueryParam;
import net.xianmu.marketing.center.domain.activity.param.BatchCheckQueryParam;
import net.xianmu.marketing.center.domain.activity.param.ScopeQueryParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicPageQueryParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityBasicInfoMapper {

    int deleteByPrimaryKey(Long id);


    int insert(ActivityBasicInfo record);


    int insertSelective(ActivityBasicInfo record);


    ActivityBasicInfo selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ActivityBasicInfo record);


    int updateByPrimaryKey(ActivityBasicInfo record);

    List<ActivityItemScopeEntity> selectByScope(BatchCheckQueryParam queryParam);

    List<ActivityItemScopeEntity> listByScope(@Param("list") List<ScopeQueryParam> list, @Param("type") Integer type,
            @Param("activityStatus") Integer activityStatus);

    ActivityBasicInfo getEffectingById(@Param("id") Long id);

    List<ActivityItemScopeEntity> listValidByScope(@Param("list") List<ScopeQueryParam> list, @Param("type") Integer type);


    List<ActivityBasicInfo> selectUnderwayByEntity(ActivityBasicInfo basicInfo);

    List<ActivityBasicInfoEntity> getPage(ActivityBasicInfoQueryParam param);

    List<ActivityBasicInfo> selectByCondition(ActivityBasicInfoQueryParam param);

    List<ActivityPageRespDTO> listByQuery(ActivityBasicPageQueryParam param);

}