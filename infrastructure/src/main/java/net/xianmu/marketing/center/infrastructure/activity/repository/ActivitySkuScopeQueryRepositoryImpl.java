package net.xianmu.marketing.center.infrastructure.activity.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityScopeConfigQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuScopeQueryRepository;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivityScopeConfigConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityScopeConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivitySkuScopeQueryRepositoryImpl implements ActivitySkuScopeQueryRepository {

    @Autowired
    private ActivityScopeConfigMapper activityScopeConfigMapper;


    @Override
    public PageInfo<ActivityScopeConfigEntity> getPage(ActivityScopeConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivityScopeConfigEntity> entities = activityScopeConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivityScopeConfigEntity selectById(Long id) {
        return ActivityScopeConfigConverter.toActivityScopeConfigEntity(activityScopeConfigMapper.selectByPrimaryKey(id));
    }


    @Override
    public List<ActivityScopeConfigEntity> selectByCondition(ActivityScopeConfigQueryParam param) {
        return ActivityScopeConfigConverter.toActivityScopeConfigEntityList(activityScopeConfigMapper.selectByCondition(param));
    }

    @Override
    public List<ActivityScopeConfigEntity> selectByInfoId(Long basicInfoId) {
        return ActivityScopeConfigConverter.toActivityScopeConfigEntityList(activityScopeConfigMapper.selectByInfoId(basicInfoId, null));
    }

    @Override
    public ActivityScopeConfigEntity selectByScopeId(Long basicInfoId) {
        return null;
    }
}
