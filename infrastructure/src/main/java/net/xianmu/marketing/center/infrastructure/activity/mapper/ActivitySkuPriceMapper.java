package net.xianmu.marketing.center.infrastructure.activity.mapper;

import java.util.List;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuPriceEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuPriceQueryParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuPrice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySkuPriceMapper {

    int deleteByPrimaryKey(Long id);


    int insert(ActivitySkuPrice record);


    int insertSelective(ActivitySkuPrice record);


    ActivitySkuPrice selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ActivitySkuPrice record);


    int updateByPrimaryKey(ActivitySkuPrice record);

    int updatePrice(ActivitySkuPrice activitySkuPrice);

    int insertBatch(@Param("list") List<ActivitySkuPrice> list);

    int deleteByBasicInfoId(@Param("basicInfoId") Long basicInfoId);

    int deleteSkuByInfoId(@Param("basicInfoId") Long basicInfoId, @Param("sku") String sku, @Param("areaNos") List<Integer> areaNos);

    int deleteByAreaNo(@Param("basicInfoId") Long basicInfoId);

    ActivitySkuPrice selectByDetailId(@Param("skuDetailId") Long skuDetailId, @Param("sku") String sku, @Param("areaNo") Integer areaNo);

    List<ActivitySkuPrice> selectBySkuAndBasicInfoId(@Param("basicInfoId") Long basicInfoId, @Param("sku") String sku);

    int deleteBySkus(@Param("basicInfoId") Long basicInfoId, @Param("skus") List<String> skus);

    int deleteBySkus(@Param("basicInfoId") Long basicInfoId, @Param("skus") List<String> skus);

    List<ActivitySkuPriceEntity> getPage(ActivitySkuPriceQueryParam param);

    List<ActivitySkuPrice> selectByCondition(ActivitySkuPriceQueryParam param);
}
