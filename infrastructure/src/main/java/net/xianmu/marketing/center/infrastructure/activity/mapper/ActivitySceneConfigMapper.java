package net.xianmu.marketing.center.infrastructure.activity.mapper;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySceneConfigQueryParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySceneConfigMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivitySceneConfig record);

    
    int insertSelective(ActivitySceneConfig record);

    
    ActivitySceneConfig selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivitySceneConfig record);

    
    int updateByPrimaryKey(ActivitySceneConfig record);

    ActivitySceneConfig selectByInfoId(@Param("basicInfoId") Long basicInfoId);

    List<ActivitySceneConfigEntity> getPage(ActivitySceneConfigQueryParam param);

    List<ActivitySceneConfig> selectByCondition(ActivitySceneConfigQueryParam param);

    void updateDelFlag(Long basicInfoId);
}