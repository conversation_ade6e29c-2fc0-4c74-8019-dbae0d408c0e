package net.xianmu.marketing.center.infrastructure.activity.repository;

import javax.annotation.Resource;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityItemConfigQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivityItemConfigQueryRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivityItemConfigConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityItemConfigMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/27
 */
@Slf4j
@Component
public class ActivityItemConfigQueryRepositoryImpl implements ActivityItemConfigQueryRepository {

    @Resource
    private ActivityItemConfigMapper activityItemConfigMapper;

    @Override
    public ActivityItemConfigEntity selectByInfoId(Long basicInfoId) {
        ActivityItemConfigEntity itemConfigEntity = activityItemConfigMapper.getByInfoId(basicInfoId);
        return itemConfigEntity;
    }

    @Override
    public PageInfo<ActivityItemConfigEntity> getPage(ActivityItemConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivityItemConfigEntity> entities = activityItemConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivityItemConfigEntity selectById(Long id) {
        return ActivityItemConfigConverter.toActivityItemConfigEntity(activityItemConfigMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<ActivityItemConfigEntity> selectByCondition(ActivityItemConfigQueryParam param) {
        return ActivityItemConfigConverter.toActivityItemConfigEntityList(activityItemConfigMapper.selectByCondition(param));
    }
}
