package net.xianmu.marketing.center.infrastructure.activity.repository;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuDetailQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivitySkuDetailConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySkuDetailMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuDetail;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivitySkuDetailQueryRepositoryImpl implements ActivitySkuDetailQueryRepository {

    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;

    @Override
    public List<ActivitySkuDetailEntity> listByItemConfigsSkus(List<Long> itemConfigIds, List<String> skus) {
        List<ActivitySkuDetail> skuDetails = activitySkuDetailMapper.listByItemConfigsSkus(itemConfigIds, skus);
        List<ActivitySkuDetailEntity> entityList = ActivitySkuDetailConverter.toActivitySkuDetailEntityList(skuDetails);
        return entityList;
    }

    @Override
    public int countByItemConfig(Long itemConfigId) {
        return activitySkuDetailMapper.countByItemConfig(itemConfigId);
    }

    @Override
    public PageInfo<ActivitySkuDetailEntity> getPage(ActivitySkuDetailQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivitySkuDetailEntity> entities = activitySkuDetailMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivitySkuDetailEntity selectById(Long id) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntity(activitySkuDetailMapper.selectByPrimaryKey(id));
    }


    @Override
    public List<ActivitySkuDetailEntity> selectByCondition(ActivitySkuDetailQueryParam param) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntityList(activitySkuDetailMapper.selectByCondition(param));
    }

    @Override
    public ActivitySkuDetailEntity selectByItemConfigIdAndSku(Long itemConfigId, String sku) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntity(activitySkuDetailMapper.selectBySku(itemConfigId, sku));
    }

    @Override
    public List<ActivitySkuDetailEntity> listByBasicInfoIds(ArrayList<Long> basicInfoIds, String sku) {
        return ActivitySkuDetailConverter.toActivitySkuDetailEntityList(activitySkuDetailMapper.listByBasicInfoIds(basicInfoIds, sku));
    }

    @Override
    public List<ActivitySkuDetailEntity> listBySkusAndOwnerId(ActivitySkuDetailQueryParam param) {
        return activitySkuDetailMapper.listBySkusAndOwnerId(param);
    }
}
