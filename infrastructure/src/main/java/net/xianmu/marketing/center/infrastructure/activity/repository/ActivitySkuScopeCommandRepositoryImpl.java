package net.xianmu.marketing.center.infrastructure.activity.repository;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityScopeConfigCommandParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuScopeCommandRepository;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivityScopeConfigConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityScopeConfigMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityScopeConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivitySkuScopeCommandRepositoryImpl implements ActivitySkuScopeCommandRepository {

    @Autowired
    private ActivityScopeConfigMapper activityScopeConfigMapper;

    @Override
    public ActivityScopeConfigEntity insertSelective(ActivityScopeConfigCommandParam param) {
        ActivityScopeConfig activityScopeConfig = ActivityScopeConfigConverter.toActivityScopeConfig(param);
        activityScopeConfigMapper.insertSelective(activityScopeConfig);
        return ActivityScopeConfigConverter.toActivityScopeConfigEntity(activityScopeConfig);
    }

    @Override
    public int updateSelectiveById(ActivityScopeConfigCommandParam param){
        return activityScopeConfigMapper.updateByPrimaryKeySelective(ActivityScopeConfigConverter.toActivityScopeConfig(param));
    }

    @Override
    public List<ActivityScopeConfigEntity> insertBatch(List<ActivityScopeConfigCommandParam> scopeConfigParams) {
        List<ActivityScopeConfig> activityScopeConfigs = ActivityScopeConfigConverter.toActivityScopeConfigs(scopeConfigParams);
        activityScopeConfigMapper.insertBatch(activityScopeConfigs);
        return ActivityScopeConfigConverter.toActivityScopeConfigEntityList(activityScopeConfigs);
    }

    @Override
    public void updateDelFlag(Long basicInfoId, Long updaterId) {
        activityScopeConfigMapper.updateDelFlag(basicInfoId, updaterId.intValue());
    }
}
