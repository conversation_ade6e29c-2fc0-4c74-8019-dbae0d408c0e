package net.xianmu.marketing.center.infrastructure.activity.repository;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuDetailCommandParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailCommandRepository;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivitySkuDetailConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySkuDetailMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivitySkuDetailCommandRepositoryImpl implements ActivitySkuDetailCommandRepository {

    @Autowired
    private ActivitySkuDetailMapper activitySkuDetailMapper;

    @Override
    public ActivitySkuDetailEntity insertSelective(ActivitySkuDetailCommandParam param) {
        ActivitySkuDetail activitySkuDetail = ActivitySkuDetailConverter.toActivitySkuDetail(param);
        activitySkuDetailMapper.insertSelective(activitySkuDetail);
        return ActivitySkuDetailConverter.toActivitySkuDetailEntity(activitySkuDetail);
    }

    @Override
    public int updateSelectiveById(ActivitySkuDetailCommandParam param){
        return activitySkuDetailMapper.updateByPrimaryKeySelective(ActivitySkuDetailConverter.toActivitySkuDetail(param));
    }

    @Override
    public List<ActivitySkuDetailEntity> insertBatch(List<ActivitySkuDetailCommandParam> skuDetailParams) {
        List<ActivitySkuDetail> activitySkuDetails = ActivitySkuDetailConverter.toActivitySkuDetails(skuDetailParams);
        if (CollectionUtils.isEmpty(activitySkuDetails)) {
            log.warn("批量插入商品配置信息为空");
            return Collections.emptyList();
        }
        activitySkuDetailMapper.insertBatch(activitySkuDetails, skuDetailParams.get(0).getItemConfigId());
        return ActivitySkuDetailConverter.toActivitySkuDetailEntityList(activitySkuDetails);
    }

    @Override
    public int updateDelFlag(Long itemConfigId, String sku) {
        return activitySkuDetailMapper.updateDelFlag(itemConfigId, sku);
    }
}
