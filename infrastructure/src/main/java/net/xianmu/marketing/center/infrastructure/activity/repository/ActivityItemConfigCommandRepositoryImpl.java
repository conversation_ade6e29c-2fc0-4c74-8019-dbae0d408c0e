package net.xianmu.marketing.center.infrastructure.activity.repository;

import java.util.List;
import javax.annotation.Resource;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityItemConfigCommandParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivityItemConfigCommandRepository;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivityBasicInfoConverter;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivityItemConfigConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityItemConfigMapper;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySkuDetailMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/28
 */
@Slf4j
@Component
public class ActivityItemConfigCommandRepositoryImpl implements ActivityItemConfigCommandRepository {

    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;

    @Autowired
    private ActivityItemConfigMapper activityItemConfigMapper;

    @Override
    public int updateBatchByItemConfigId(Long itemConfigId, List<String> skus) {
        return activitySkuDetailMapper.updateBatchByItemConfigId(itemConfigId, skus);
    }

    @Override
    public ActivityItemConfigEntity insertSelective(ActivityItemConfigCommandParam param) {
        ActivityItemConfig activityItemConfig = ActivityItemConfigConverter.toActivityItemConfig(param);
        activityItemConfigMapper.insertSelective(activityItemConfig);
        return ActivityItemConfigConverter.toActivityItemConfigEntity(activityItemConfig);
    }

    @Override
    public int updateSelectiveById(ActivityItemConfigCommandParam param){
        return activityItemConfigMapper.updateByPrimaryKeySelective(ActivityItemConfigConverter.toActivityItemConfig(param));
    }

    @Override
    public Long updateDelFlag(ActivityItemConfigCommandParam param) {
        ActivityItemConfig activityItemConfig = ActivityItemConfigConverter.toActivityItemConfig(param);
        activityItemConfigMapper.updateDelFlag(activityItemConfig);
        return activityItemConfig.getId();
    }
}
