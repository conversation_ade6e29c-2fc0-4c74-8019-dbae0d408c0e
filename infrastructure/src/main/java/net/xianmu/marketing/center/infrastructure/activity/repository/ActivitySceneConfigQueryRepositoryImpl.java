package net.xianmu.marketing.center.infrastructure.activity.repository;

import net.xianmu.marketing.center.domain.activity.repository.ActivitySceneConfigQueryRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySceneConfigQueryParam;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivitySceneConfigConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySceneConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-06-10 15:41:35
* @version 1.0
*
*/
@Repository
public class ActivitySceneConfigQueryRepositoryImpl implements ActivitySceneConfigQueryRepository {

    @Autowired
    private ActivitySceneConfigMapper activitySceneConfigMapper;


    @Override
    public PageInfo<ActivitySceneConfigEntity> getPage(ActivitySceneConfigQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivitySceneConfigEntity> entities = activitySceneConfigMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public ActivitySceneConfigEntity selectById(Long id) {
        return ActivitySceneConfigConverter.toActivitySceneConfigEntity(activitySceneConfigMapper.selectByPrimaryKey(id));
    }


    @Override
    public List<ActivitySceneConfigEntity> selectByCondition(ActivitySceneConfigQueryParam param) {
        return ActivitySceneConfigConverter.toActivitySceneConfigEntityList(activitySceneConfigMapper.selectByCondition(param));
    }

    @Override
    public ActivitySceneConfigEntity selectByBasicInfoId(Long basicInfoId) {
        return ActivitySceneConfigConverter.toActivitySceneConfigEntity(activitySceneConfigMapper.selectByInfoId(basicInfoId));
    }
}