package net.xianmu.marketing.center.infrastructure.activity.mapper;

import java.util.List;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuDetailQueryParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivitySkuDetailMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(ActivitySkuDetail record);


    ActivitySkuDetail selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ActivitySkuDetail record);

    int insertBatch(@Param("list") List<ActivitySkuDetail> list, @Param("itemConfigId") Long itemConfigId);

    ActivitySkuDetail selectBySku(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> selectByItemConfig(@Param("itemConfigId") Long itemConfigId);

    int countByItemConfig(@Param("itemConfigId") Long itemConfigId);

    int updateDelFlag(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> listByItemConfigs(@Param("list") List<Long> list, @Param("sku") String sku);

    List<ActivitySkuDetail> listByItemConfigsSkus(@Param("configList") List<Long> list, @Param("skus") List<String> skus);

    int updateDelFlagBatch(@Param("ids") List<Long> ids);

    int deleteSkuByConfigId(@Param("itemConfigId") Long itemConfigId, @Param("sku") String sku);

    List<ActivitySkuDetail> listByBasicInfoIds(@Param("list") List<Long> basicInfoIds, @Param("sku") String sku);

    List<String> listSkuByItemConfigIdStrings(@Param("list") List<Long> itemConfigIds);

    int updateBatchByItemConfigId(@Param("itemConfigId") Long itemConfigId, @Param("skus") List<String> skus);

    List<ActivitySkuDetailEntity> getPage(ActivitySkuDetailQueryParam param);

    List<ActivitySkuDetail> selectByCondition(ActivitySkuDetailQueryParam param);

    List<ActivitySkuDetailEntity> listBySkusAndOwnerId(ActivitySkuDetailQueryParam param);
}