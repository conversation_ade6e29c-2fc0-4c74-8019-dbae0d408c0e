package net.xianmu.marketing.center.infrastructure.activity.converter;

import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityScopeConfigCommandParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityScopeConfig;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
public class ActivityScopeConfigConverter {

    private ActivityScopeConfigConverter() {
        // 无需实现
    }




    public static List<ActivityScopeConfigEntity> toActivityScopeConfigEntityList(List<ActivityScopeConfig> activityScopeConfigList) {
        if (activityScopeConfigList == null) {
            return Collections.emptyList();
        }
        List<ActivityScopeConfigEntity> activityScopeConfigEntityList = new ArrayList<>();
        for (ActivityScopeConfig activityScopeConfig : activityScopeConfigList) {
            activityScopeConfigEntityList.add(toActivityScopeConfigEntity(activityScopeConfig));
        }
        return activityScopeConfigEntityList;
}


    public static ActivityScopeConfigEntity toActivityScopeConfigEntity(ActivityScopeConfig activityScopeConfig) {
        if (activityScopeConfig == null) {
             return null;
        }
        ActivityScopeConfigEntity activityScopeConfigEntity = new ActivityScopeConfigEntity();
        activityScopeConfigEntity.setId(activityScopeConfig.getId());
        activityScopeConfigEntity.setBasicInfoId(activityScopeConfig.getBasicInfoId());
        activityScopeConfigEntity.setScopeId(activityScopeConfig.getScopeId());
        activityScopeConfigEntity.setScopeType(activityScopeConfig.getScopeType());
        activityScopeConfigEntity.setUpdaterId(activityScopeConfig.getUpdaterId());
        activityScopeConfigEntity.setDelFlag(activityScopeConfig.getDelFlag());
        activityScopeConfigEntity.setCreateTime(activityScopeConfig.getCreateTime());
        activityScopeConfigEntity.setUpdateTime(activityScopeConfig.getUpdateTime());
        return activityScopeConfigEntity;
    }








    public static ActivityScopeConfig toActivityScopeConfig(ActivityScopeConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        ActivityScopeConfig activityScopeConfig = new ActivityScopeConfig();
        activityScopeConfig.setId(param.getId());
        activityScopeConfig.setBasicInfoId(param.getBasicInfoId());
        activityScopeConfig.setScopeId(param.getScopeId());
        activityScopeConfig.setScopeType(param.getScopeType());
        activityScopeConfig.setUpdaterId(param.getUpdaterId());
        activityScopeConfig.setDelFlag(param.getDelFlag());
        activityScopeConfig.setCreateTime(param.getCreateTime());
        activityScopeConfig.setUpdateTime(param.getUpdateTime());
        return activityScopeConfig;
    }

    public static List<ActivityScopeConfig> toActivityScopeConfigs(List<ActivityScopeConfigCommandParam> scopeConfigParams) {
        if (CollectionUtils.isEmpty(scopeConfigParams)) {
            return Collections.emptyList();
        }
        List<ActivityScopeConfig> activityScopeConfigs = new ArrayList<>();
        for (ActivityScopeConfigCommandParam scopeConfigParam : scopeConfigParams) {
            ActivityScopeConfig activityScopeConfig = toActivityScopeConfig(scopeConfigParam);
            if (activityScopeConfig == null) {
                continue;
            }
            activityScopeConfigs.add(activityScopeConfig);
        }
        return activityScopeConfigs;
    }
}
