package net.xianmu.marketing.center.infrastructure.activity.converter;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySceneConfigCommandParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
public class ActivitySceneConfigConverter {

    private ActivitySceneConfigConverter() {
        // 无需实现
    }




    public static List<ActivitySceneConfigEntity> toActivitySceneConfigEntityList(List<ActivitySceneConfig> activitySceneConfigList) {
        if (activitySceneConfigList == null) {
            return Collections.emptyList();
        }
        List<ActivitySceneConfigEntity> activitySceneConfigEntityList = new ArrayList<>();
        for (ActivitySceneConfig activitySceneConfig : activitySceneConfigList) {
            activitySceneConfigEntityList.add(toActivitySceneConfigEntity(activitySceneConfig));
        }
        return activitySceneConfigEntityList;
}


    public static ActivitySceneConfigEntity toActivitySceneConfigEntity(ActivitySceneConfig activitySceneConfig) {
        if (activitySceneConfig == null) {
             return null;
        }
        ActivitySceneConfigEntity activitySceneConfigEntity = new ActivitySceneConfigEntity();
        activitySceneConfigEntity.setId(activitySceneConfig.getId());
        activitySceneConfigEntity.setBasicInfoId(activitySceneConfig.getBasicInfoId());
        activitySceneConfigEntity.setPlatform(activitySceneConfig.getPlatform());
        activitySceneConfigEntity.setPlace(activitySceneConfig.getPlace());
        activitySceneConfigEntity.setDelFlag(activitySceneConfig.getDelFlag());
        activitySceneConfigEntity.setCreateTime(activitySceneConfig.getCreateTime());
        activitySceneConfigEntity.setUpdateTime(activitySceneConfig.getUpdateTime());
        return activitySceneConfigEntity;
    }








    public static ActivitySceneConfig toActivitySceneConfig(ActivitySceneConfigCommandParam param) {
        if (param == null) {
            return null;
        }
        ActivitySceneConfig activitySceneConfig = new ActivitySceneConfig();
        activitySceneConfig.setId(param.getId());
        activitySceneConfig.setBasicInfoId(param.getBasicInfoId());
        activitySceneConfig.setPlatform(param.getPlatform());
        activitySceneConfig.setPlace(param.getPlace());
        activitySceneConfig.setDelFlag(param.getDelFlag());
        activitySceneConfig.setCreateTime(param.getCreateTime());
        activitySceneConfig.setUpdateTime(param.getUpdateTime());
        return activitySceneConfig;
    }
}
