package net.xianmu.marketing.center.infrastructure.activity.converter;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuPriceEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuPriceCommandParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuPrice;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
public class ActivitySkuPriceConverter {

    private ActivitySkuPriceConverter() {
        // 无需实现
    }




    public static List<ActivitySkuPriceEntity> toActivitySkuPriceEntityList(List<ActivitySkuPrice> activitySkuPriceList) {
        if (activitySkuPriceList == null) {
            return Collections.emptyList();
        }
        List<ActivitySkuPriceEntity> activitySkuPriceEntityList = new ArrayList<>();
        for (ActivitySkuPrice activitySkuPrice : activitySkuPriceList) {
            activitySkuPriceEntityList.add(toActivitySkuPriceEntity(activitySkuPrice));
        }
        return activitySkuPriceEntityList;
}


    public static ActivitySkuPriceEntity toActivitySkuPriceEntity(ActivitySkuPrice activitySkuPrice) {
        if (activitySkuPrice == null) {
             return null;
        }
        ActivitySkuPriceEntity activitySkuPriceEntity = new ActivitySkuPriceEntity();
        activitySkuPriceEntity.setId(activitySkuPrice.getId());
        activitySkuPriceEntity.setSkuDetailId(activitySkuPrice.getSkuDetailId());
        activitySkuPriceEntity.setSku(activitySkuPrice.getSku());
        activitySkuPriceEntity.setAreaNo(activitySkuPrice.getAreaNo());
        activitySkuPriceEntity.setSalePrice(activitySkuPrice.getSalePrice());
        activitySkuPriceEntity.setLadderPrice(activitySkuPrice.getLadderPrice());
        activitySkuPriceEntity.setActivityPrice(activitySkuPrice.getActivityPrice());
        activitySkuPriceEntity.setUpdaterId(activitySkuPrice.getUpdaterId());
        activitySkuPriceEntity.setCreateTime(activitySkuPrice.getCreateTime());
        activitySkuPriceEntity.setUpdateTime(activitySkuPrice.getUpdateTime());
        activitySkuPriceEntity.setBasicInfoId(activitySkuPrice.getBasicInfoId());
        return activitySkuPriceEntity;
    }








    public static ActivitySkuPrice toActivitySkuPrice(ActivitySkuPriceCommandParam param) {
        if (param == null) {
            return null;
        }
        ActivitySkuPrice activitySkuPrice = new ActivitySkuPrice();
        activitySkuPrice.setId(param.getId());
        activitySkuPrice.setSkuDetailId(param.getSkuDetailId());
        activitySkuPrice.setSku(param.getSku());
        activitySkuPrice.setAreaNo(param.getAreaNo());
        activitySkuPrice.setSalePrice(param.getSalePrice());
        activitySkuPrice.setLadderPrice(param.getLadderPrice());
        activitySkuPrice.setActivityPrice(param.getActivityPrice());
        activitySkuPrice.setUpdaterId(param.getUpdaterId());
        activitySkuPrice.setCreateTime(param.getCreateTime());
        activitySkuPrice.setUpdateTime(param.getUpdateTime());
        activitySkuPrice.setBasicInfoId(param.getBasicInfoId());
        return activitySkuPrice;
    }
}
