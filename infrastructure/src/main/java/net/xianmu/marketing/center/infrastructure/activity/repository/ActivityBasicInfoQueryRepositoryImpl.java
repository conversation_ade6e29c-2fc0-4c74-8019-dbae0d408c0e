package net.xianmu.marketing.center.infrastructure.activity.repository;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.dto.ActivityPageRespDTO;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicPageQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivityBasicInfoQueryRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemScopeEntity;
import net.xianmu.marketing.center.domain.activity.param.BatchCheckQueryParam;
import net.xianmu.marketing.center.domain.activity.param.ScopeQueryParam;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivityBasicInfoConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityBasicInfoMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivityBasicInfoQueryRepositoryImpl implements ActivityBasicInfoQueryRepository {

    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;

    @Override
    public List<ActivityItemScopeEntity> listByScope(List<ScopeQueryParam> list, Integer type, Integer activityStatus) {
        List<ActivityItemScopeEntity> entityList = activityBasicInfoMapper.listByScope(list, type, activityStatus);
        return entityList;
    }

    @Override
    public List<ActivityItemScopeEntity> selectByScope(BatchCheckQueryParam queryParam) {
        List<ActivityItemScopeEntity> entityList = activityBasicInfoMapper.selectByScope(queryParam);
        return entityList;
    }

    @Override
    public ActivityBasicInfoEntity selectById(Long id) {
        ActivityBasicInfo activityBasicInfo = activityBasicInfoMapper.selectByPrimaryKey(id);
        ActivityBasicInfoEntity basicInfoEntity = ActivityBasicInfoConverter.toActivityBasicInfoEntity(activityBasicInfo);
        return basicInfoEntity;
    }

    @Override
    public PageInfo<ActivityBasicInfoEntity> getPage(ActivityBasicInfoQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ActivityBasicInfoEntity> entities = activityBasicInfoMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public List<ActivityBasicInfoEntity> selectByCondition(ActivityBasicInfoQueryParam param) {
        return ActivityBasicInfoConverter.toActivityBasicInfoEntityList(activityBasicInfoMapper.selectByCondition(param));
    }

    @Override
    public List<ActivityPageRespDTO> listByQuery(ActivityBasicPageQueryParam param) {
        return activityBasicInfoMapper.listByQuery(param);
    }
}
