package net.xianmu.marketing.center.infrastructure.activity.repository;

import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuPriceEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuPriceCommandParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuPriceCommandRepository;
import net.xianmu.marketing.center.infrastructure.activity.converter.ActivitySkuPriceConverter;
import net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySkuPriceMapper;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuPrice;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Component
public class ActivitySkuPriceCommandRepositoryImpl implements ActivitySkuPriceCommandRepository {

    @Resource
    private ActivitySkuPriceMapper activitySkuPriceMapper;

    @Override
    public int deleteBySkus(Long basicInfoId, List<String> skus) {
        return activitySkuPriceMapper.deleteBySkus(basicInfoId, skus);
    }

    @Override
    public int deleteByAreaNos(Long basicInfoId, List<Integer> areaNos) {
        return activitySkuPriceMapper.deleteBySkus(basicInfoId, skus);
    }

    @Override
    public ActivitySkuPriceEntity insertSelective(ActivitySkuPriceCommandParam param) {
        ActivitySkuPrice activitySkuPrice = ActivitySkuPriceConverter.toActivitySkuPrice(param);
        activitySkuPriceMapper.insertSelective(activitySkuPrice);
        return ActivitySkuPriceConverter.toActivitySkuPriceEntity(activitySkuPrice);
    }

    @Override
    public int updateSelectiveById(ActivitySkuPriceCommandParam param){
        return activitySkuPriceMapper.updateByPrimaryKeySelective(ActivitySkuPriceConverter.toActivitySkuPrice(param));
    }

    @Override
    public void deleteByInfoId(Long basicInfoId) {
        activitySkuPriceMapper.deleteByBasicInfoId(basicInfoId);
    }
}
