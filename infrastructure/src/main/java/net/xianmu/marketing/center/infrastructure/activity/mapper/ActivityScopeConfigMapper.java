package net.xianmu.marketing.center.infrastructure.activity.mapper;

import java.util.Collection;
import java.util.List;

import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityScopeConfigQueryParam;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo;
import net.xianmu.marketing.center.infrastructure.activity.model.ActivityScopeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityScopeConfigMapper {

    int deleteByPrimaryKey(Long id);


    int insert(ActivityScopeConfig record);


    int insertSelective(ActivityScopeConfig record);


    ActivityScopeConfig selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ActivityScopeConfig record);


    int updateByPrimaryKey(ActivityScopeConfig record);

    int updateDelFlag(@Param("basicInfoId") Long basicInfoId, @Param("adminId") Integer adminId);

    int insertBatch(@Param("list") List<ActivityScopeConfig> list);

    List<ActivityScopeConfig> selectByInfoId(@Param("basicInfoId") Long basicInfoId, @Param("limit") Integer limit);

    int countByBasicInfoId(@Param("basicInfoId") Long basicInfoId);

    ActivityScopeConfig selectByScopeId(@Param("basicInfoId") Long basicInfoId, @Param("scopeId") Long scopeId);

    ActivityBasicInfo selectExpiredActivity(@Param("scopeId") Long ScopeId);

    /**
     * 获取所有临保活动的城市
     * @return
     */
    List<ActivityScopeConfig> selectAllExpiredScope(@Param("areaNos") Collection<Integer> areaNos);

    List<ActivityScopeConfig> listByBasicInfoIds(@Param("list") List<Long> basicInfoIds);

    List<ActivityScopeConfigEntity> getPage(ActivityScopeConfigQueryParam param);

    List<ActivityScopeConfig> selectByCondition(ActivityScopeConfigQueryParam param);
}
