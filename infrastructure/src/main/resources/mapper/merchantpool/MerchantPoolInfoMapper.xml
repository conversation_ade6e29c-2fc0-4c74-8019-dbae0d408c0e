<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.marketing.center.infrastructure.merchantpool.mapper.MerchantPoolInfoMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.marketing.center.infrastructure.merchantpool.model.MerchantPoolInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="create_way" jdbcType="TINYINT" property="createWay"/>
    <result column="update_way" jdbcType="TINYINT" property="updateWay"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="version" jdbcType="INTEGER" property="version"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="updater" jdbcType="VARCHAR" property="updater"/>
    <result column="data_source" jdbcType="INTEGER" property="dataSource"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `name`, `create_way`, `update_way`, `status`, `remark`, `version`,
    `creator`, `updater`, `create_time`, `update_time`, `data_source`
  </sql>

  <!-- 修改字段SQL -->
  <sql id="whereColumnByUpdate">
    <trim prefix="SET" suffixOverrides=",">
      <if test="name != null">
        t.name = #{name},
      </if>
      <if test="createWay != null">
        t.create_way = #{createWay},
      </if>
      <if test="updateWay != null">
        t.update_way = #{updateWay},
      </if>
      <if test="status != null">
        t.status = #{status},
      </if>
      <if test="remark != null">
        t.remark = #{remark},
      </if>
      <if test="version != null">
        t.version = #{version},
      </if>
      <if test="creator != null">
        t.creator = #{creator},
      </if>
      <if test="updater != null">
        t.updater = #{updater},
      </if>
      <if test="createTime != null">
        t.create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        t.update_time = #{updateTime},
      </if>
      <if test="dataSource != null">
        t.data_source = #{dataSource},
      </if>
    </trim>
  </sql>

  <!-- 查询条件SQL -->
  <sql id="whereColumnBySelect">
    <trim prefix="WHERE" prefixOverrides="AND | OR">
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="name != null and name !=''">
        AND t.name = #{name}
      </if>
      <if test="createWay != null">
        AND t.create_way = #{createWay}
      </if>
      <if test="updateWay != null">
        AND t.update_way = #{updateWay}
      </if>
      <if test="status != null">
        AND t.status = #{status}
      </if>
      <if test="remark != null and remark !=''">
        AND t.remark = #{remark}
      </if>
      <if test="version != null">
        AND t.version = #{version}
      </if>
      <if test="creator != null and creator !=''">
        AND t.creator = #{creator}
      </if>
      <if test="updater != null and updater !=''">
        AND t.updater = #{updater}
      </if>
      <if test="createTime != null">
        AND t.create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        AND t.update_time = #{updateTime}
      </if>
      <if test="dataSource != null">
        AND t.data_source = #{dataSource}
      </if>
      <if test="scopeIds != null">
        AND t.id in
        <foreach collection="scopeIds" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </trim>
  </sql>

  <select id="listInfoByIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_pool_info
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

    <select id="getPage" resultType="net.xianmu.marketing.center.domain.merchantpool.entity.MerchantPoolInfoEntity">
      select
          t.id id,
          t.name name,
          t.create_way createWay,
          t.update_way updateWay,
          t.status status,
          t.remark remark,
          t.version version,
          t.creator creator,
          t.updater updater,
          t.create_time createTime,
          t.update_time updateTime,
          t.data_source dataSource
      from
      merchant_pool_info t
      <where>
        <if test="id != null">
          and t.id = #{id}
        </if>
        <if test="name != null">
          and t.`name` like CONCAT(#{name},'%')
        </if>
        <if test="creator != null">
          and t.`creator` like CONCAT(#{creator},'%')
        </if>
        <if test="createWay != null">
          and t.`create_way` = #{createWay}
        </if>
        <if test="updateWay != null">
          and t.`update_way` = #{updateWay}
        </if>
        <if test="status != null">
          and t.`status` = #{status}
        </if>
      </where>
      order by t.id desc
    </select>

  <select id="listCreators" resultType="string">
    select distinct creator
    from merchant_pool_info
    where creator like CONCAT(#{creator}, '%')
  </select>

  <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
  <insert id="insertSelective" parameterType="net.xianmu.marketing.center.infrastructure.merchantpool.model.MerchantPoolInfo" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO merchant_pool_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="createWay != null">
        create_way,
      </if>
      <if test="updateWay != null">
        update_way,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null">
        #{id,jdbcType=NUMERIC},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createWay != null">
        #{createWay,jdbcType=TINYINT},
      </if>
      <if test="updateWay != null">
        #{updateWay,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <!-- 根据主键ID进行修改 -->
  <update id="updateSelectiveById" parameterType="net.xianmu.marketing.center.infrastructure.merchantpool.model.MerchantPoolInfo" >
    UPDATE merchant_pool_info t
    <include refid="whereColumnByUpdate"></include>
    <where>
      t.id = #{id,jdbcType=NUMERIC}
    </where>
  </update>



  <!-- 根据主键ID进行物理删除 -->
  <delete id="remove">
    DELETE FROM merchant_pool_info t
    WHERE t.id = #{id,jdbcType=NUMERIC}
  </delete>

  <!-- 根据主键ID获取数据 -->
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap" >
    SELECT <include refid="Base_Column_List" />
    FROM merchant_pool_info t
    WHERE t.id = #{id}
  </select>

  <!-- 根据条件查询对象 -->
  <select id="selectByCondition" parameterType="net.xianmu.marketing.center.domain.merchantpool.param.query.MerchantPoolInfoQueryParam" resultMap="BaseResultMap" >
    SELECT <include refid="Base_Column_List" />
    FROM merchant_pool_info t
    <include refid="whereColumnBySelect"></include>
  </select>

  <select id="getMerchantPoolInfo" resultType="java.lang.Long">
    select mpd.pool_info_id
    from merchant_pool_detail mpd right join merchant_pool_info mpi on mpd.pool_info_id = mpi.id and mpd.version = mpi.version
    where m_id = #{mId}
  </select>
</mapper>