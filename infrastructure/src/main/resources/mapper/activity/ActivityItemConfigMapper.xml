<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityItemConfigMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="basic_info_id" jdbcType="BIGINT" property="basicInfoId" />
    <result column="good_select_way" jdbcType="TINYINT" property="goodSelectWay" />
    <result column="pricing_type" jdbcType="TINYINT" property="pricingType" />
    <result column="pricing_type_ext" jdbcType="VARCHAR" property="pricingTypeExt" />
    <result column="discount_percentage" jdbcType="INTEGER" property="discountPercentage" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="EntityResultMap" type="net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="basic_info_id" jdbcType="BIGINT" property="basicInfoId" />
    <result column="good_select_way" jdbcType="TINYINT" property="goodSelectWay" />
    <result column="pricing_type" jdbcType="TINYINT" property="pricingType" />
    <result column="pricing_type_ext" jdbcType="VARCHAR" property="pricingTypeExt" />
    <result column="discount_percentage" jdbcType="INTEGER" property="discountPercentage" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
  </resultMap>

  <sql id="Base_Column_List">
    `id`, `basic_info_id`, `good_select_way`, `pricing_type`, `pricing_type_ext`, `discount_percentage`, 
    `discount`, `updater_id`, `del_flag`, `create_time`, `update_time`
  </sql>

  <!-- 列定义 -->
  <sql id="activityItemConfigColumns">
    t.id,
          t.basic_info_id,
          t.good_select_way,
          t.pricing_type,
          t.pricing_type_ext,
          t.discount_percentage,
          t.discount,
          t.updater_id,
          t.del_flag,
          t.create_time,
          t.update_time
  </sql>

  <!-- 查询条件SQL -->
  <sql id="whereColumnBySelect">
    <trim prefix="WHERE" prefixOverrides="AND | OR">
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="basicInfoId != null">
        AND t.basic_info_id = #{basicInfoId}
      </if>
      <if test="goodSelectWay != null">
        AND t.good_select_way = #{goodSelectWay}
      </if>
      <if test="pricingType != null">
        AND t.pricing_type = #{pricingType}
      </if>
      <if test="pricingTypeExt != null and pricingTypeExt !=''">
        AND t.pricing_type_ext = #{pricingTypeExt}
      </if>
      <if test="discountPercentage != null">
        AND t.discount_percentage = #{discountPercentage}
      </if>
      <if test="discount != null">
        AND t.discount = #{discount}
      </if>
      <if test="updaterId != null">
        AND t.updater_id = #{updaterId}
      </if>
      <if test="delFlag != null">
        AND t.del_flag = #{delFlag}
      </if>
      <if test="createTime != null">
        AND t.create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        AND t.update_time = #{updateTime}
      </if>
    </trim>
  </sql>

  <!-- 修改字段SQL -->
  <sql id="whereColumnByUpdate">
    <trim prefix="SET" suffixOverrides=",">
      <if test="basicInfoId != null">
        t.basic_info_id = #{basicInfoId},
      </if>
      <if test="goodSelectWay != null">
        t.good_select_way = #{goodSelectWay},
      </if>
      <if test="pricingType != null">
        t.pricing_type = #{pricingType},
      </if>
      <if test="pricingTypeExt != null">
        t.pricing_type_ext = #{pricingTypeExt},
      </if>
      <if test="discountPercentage != null">
        t.discount_percentage = #{discountPercentage},
      </if>
      <if test="discount != null">
        t.discount = #{discount},
      </if>
      <if test="updaterId != null">
        t.updater_id = #{updaterId},
      </if>
      <if test="delFlag != null">
        t.del_flag = #{delFlag},
      </if>
      <if test="createTime != null">
        t.create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        t.update_time = #{updateTime},
      </if>
    </trim>
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from activity_item_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from activity_item_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig">
    insert into activity_item_config (`id`, `basic_info_id`, `good_select_way`, 
      `pricing_type`, `pricing_type_ext`, `discount_percentage`, 
      `discount`, `updater_id`, `del_flag`, 
      `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{basicInfoId,jdbcType=BIGINT}, #{goodSelectWay,jdbcType=TINYINT}, 
      #{pricingType,jdbcType=TINYINT}, #{pricingTypeExt,jdbcType=VARCHAR}, #{discountPercentage,jdbcType=INTEGER}, 
      #{discount,jdbcType=DECIMAL}, #{updaterId,jdbcType=INTEGER}, #{delFlag,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig" useGeneratedKeys="true" keyProperty="id">
    insert into activity_item_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="basicInfoId != null">
        `basic_info_id`,
      </if>
      <if test="goodSelectWay != null">
        `good_select_way`,
      </if>
      <if test="pricingType != null">
        `pricing_type`,
      </if>
      <if test="pricingTypeExt != null">
        `pricing_type_ext`,
      </if>
      <if test="discountPercentage != null">
        `discount_percentage`,
      </if>
      <if test="discount != null">
        `discount`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="basicInfoId != null">
        #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="goodSelectWay != null">
        #{goodSelectWay,jdbcType=TINYINT},
      </if>
      <if test="pricingType != null">
        #{pricingType,jdbcType=TINYINT},
      </if>
      <if test="pricingTypeExt != null">
        #{pricingTypeExt,jdbcType=VARCHAR},
      </if>
      <if test="discountPercentage != null">
        #{discountPercentage,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig">
    update activity_item_config
    <set>
      <if test="basicInfoId != null">
        `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="goodSelectWay != null">
        `good_select_way` = #{goodSelectWay,jdbcType=TINYINT},
      </if>
      <if test="pricingType != null">
        `pricing_type` = #{pricingType,jdbcType=TINYINT},
      </if>
      <if test="pricingTypeExt != null">
        `pricing_type_ext` = #{pricingTypeExt,jdbcType=VARCHAR},
      </if>
      <if test="discountPercentage != null">
        `discount_percentage` = #{discountPercentage,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        `discount` = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityItemConfig">
    update activity_item_config
    set `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      `good_select_way` = #{goodSelectWay,jdbcType=TINYINT},
      `pricing_type` = #{pricingType,jdbcType=TINYINT},
      `pricing_type_ext` = #{pricingTypeExt,jdbcType=VARCHAR},
      `discount_percentage` = #{discountPercentage,jdbcType=INTEGER},
      `discount` = #{discount,jdbcType=DECIMAL},
      `updater_id` = #{updaterId,jdbcType=INTEGER},
      `del_flag` = #{delFlag,jdbcType=TINYINT},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateDelFlag" useGeneratedKeys="true" keyProperty="id">
    update activity_item_config
    set del_flag = 1, updater_id = #{updaterId}
    where del_flag = 0 and basic_info_id = #{basicInfoId}
  </update>

  <select id="getByInfoId" resultMap="EntityResultMap">
    select
    <include refid="Base_Column_List" />
    from activity_item_config
    where basic_info_id = #{basicInfoId} and del_flag = 0
  </select>

  <!-- 查询列表可以根据分页进行查询 -->
  <select id="getPage" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivityItemConfigQueryParam"  resultType="net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity" >
    SELECT
    t.id id,
    t.basic_info_id basicInfoId,
    t.good_select_way goodSelectWay,
    t.pricing_type pricingType,
    t.pricing_type_ext pricingTypeExt,
    t.discount_percentage discountPercentage,
    t.discount discount,
    t.updater_id updaterId,
    t.del_flag delFlag,
    t.create_time createTime,
    t.update_time updateTime
    FROM activity_item_config t
    <include refid="whereColumnBySelect" />
    ORDER BY t.id DESC
  </select>


  <!-- 根据条件查询对象 -->
  <select id="selectByCondition" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivityItemConfigQueryParam" resultMap="BaseResultMap" >
    SELECT <include refid="activityItemConfigColumns" />
    FROM activity_item_config t
    <include refid="whereColumnBySelect"></include>
  </select>
</mapper>