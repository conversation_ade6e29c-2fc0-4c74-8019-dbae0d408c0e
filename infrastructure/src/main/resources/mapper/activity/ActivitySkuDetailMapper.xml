<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySkuDetailMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuDetail">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="item_config_id" jdbcType="BIGINT" property="itemConfigId"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="rounding_mode" jdbcType="TINYINT" property="roundingMode"/>
    <result column="adjust_type" jdbcType="TINYINT" property="adjustType"/>
    <result column="amount" jdbcType="DECIMAL" property="amount"/>
    <result column="sort" jdbcType="INTEGER" property="sort"/>
    <result column="plan_quantity" jdbcType="INTEGER" property="planQuantity"/>
    <result column="actual_quantity" jdbcType="INTEGER" property="actualQuantity"/>
    <result column="lock_quantity" jdbcType="INTEGER" property="lockQuantity"/>
    <result column="account_limit" jdbcType="TINYINT" property="accountLimit"/>
    <result column="limit_quantity" jdbcType="INTEGER" property="limitQuantity"/>
    <result column="min_sale_num" jdbcType="INTEGER" property="minSaleNum"/>
    <result column="single_deposit" jdbcType="DECIMAL" property="singleDeposit"/>
    <result column="expansion_ratio" jdbcType="DECIMAL" property="expansionRatio"/>
    <result column="hide_price" jdbcType="TINYINT" property="hidePrice"/>
    <result column="timing_config" jdbcType="VARCHAR" property="timingConfig"/>
    <result column="is_support_timing" jdbcType="TINYINT" property="isSupportTiming"/>
    <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
    <result column="auto_price" jdbcType="TINYINT" property="autoPrice"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="marketing_cost_sharing_ratio" jdbcType="VARCHAR" property="marketingCostSharingRatio"/>
    <result column="discount_label" jdbcType="TINYINT" property="discountLabel"/>
    <result column="ladder_config" property="ladderConfig" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `item_config_id`, `sku`, `rounding_mode`, `adjust_type`, `amount`, `sort`, `plan_quantity`,
    `actual_quantity`, `lock_quantity`, `account_limit`, `limit_quantity`, `min_sale_num`,
    `single_deposit`, `expansion_ratio`, `hide_price`, `timing_config`,`is_support_timing`, `del_flag`,  `auto_price`, `create_time`,
    `update_time`, `discount_label`, ladder_config, marketing_cost_sharing_ratio
  </sql>
  <!-- 列定义 -->
  <sql id="activitySkuDetailColumns">
    t.id,
          t.item_config_id,
          t.sku,
          t.rounding_mode,
          t.adjust_type,
          t.amount,
          t.sort,
          t.plan_quantity,
          t.actual_quantity,
          t.lock_quantity,
          t.account_limit,
          t.limit_quantity,
          t.min_sale_num,
          t.single_deposit,
          t.expansion_ratio,
          t.hide_price,
          t.timing_config,
          t.del_flag,
          t.create_time,
          t.update_time,
          t.is_support_timing,
          t.auto_price,
          t.discount_label,
          t.ladder_config,
          t.marketing_cost_sharing_ratio
  </sql>

  <!-- 查询条件SQL -->
  <sql id="whereColumnBySelect">
    <trim prefix="WHERE" prefixOverrides="AND | OR">
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="itemConfigId != null">
        AND t.item_config_id = #{itemConfigId}
      </if>
      <if test="sku != null and sku !=''">
        AND t.sku = #{sku}
      </if>
      <if test="roundingMode != null">
        AND t.rounding_mode = #{roundingMode}
      </if>
      <if test="adjustType != null">
        AND t.adjust_type = #{adjustType}
      </if>
      <if test="amount != null">
        AND t.amount = #{amount}
      </if>
      <if test="sort != null">
        AND t.sort = #{sort}
      </if>
      <if test="planQuantity != null">
        AND t.plan_quantity = #{planQuantity}
      </if>
      <if test="actualQuantity != null">
        AND t.actual_quantity = #{actualQuantity}
      </if>
      <if test="lockQuantity != null">
        AND t.lock_quantity = #{lockQuantity}
      </if>
      <if test="accountLimit != null">
        AND t.account_limit = #{accountLimit}
      </if>
      <if test="limitQuantity != null">
        AND t.limit_quantity = #{limitQuantity}
      </if>
      <if test="minSaleNum != null">
        AND t.min_sale_num = #{minSaleNum}
      </if>
      <if test="singleDeposit != null">
        AND t.single_deposit = #{singleDeposit}
      </if>
      <if test="expansionRatio != null">
        AND t.expansion_ratio = #{expansionRatio}
      </if>
      <if test="hidePrice != null">
        AND t.hide_price = #{hidePrice}
      </if>
      <if test="timingConfig != null and timingConfig !=''">
        AND t.timing_config = #{timingConfig}
      </if>
      <if test="delFlag != null">
        AND t.del_flag = #{delFlag}
      </if>
      <if test="createTime != null">
        AND t.create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        AND t.update_time = #{updateTime}
      </if>
      <if test="isSupportTiming != null">
        AND t.is_support_timing = #{isSupportTiming}
      </if>
      <if test="autoPrice != null">
        AND t.auto_price = #{autoPrice}
      </if>
      <if test="discountLabel != null">
        AND t.discount_label = #{discountLabel}
      </if>
      <if test="ladderConfig != null and ladderConfig !=''">
        AND t.ladder_config = #{ladderConfig}
      </if>
      <if test="marketingCostSharingRatio != null and marketingCostSharingRatio !=''">
        AND t.marketing_cost_sharing_ratio = #{marketingCostSharingRatio}
      </if>
    </trim>
  </sql>

  <!-- 修改字段SQL -->
  <sql id="whereColumnByUpdate">
    <trim prefix="SET" suffixOverrides=",">
      <if test="itemConfigId != null">
        t.item_config_id = #{itemConfigId},
      </if>
      <if test="sku != null">
        t.sku = #{sku},
      </if>
      <if test="roundingMode != null">
        t.rounding_mode = #{roundingMode},
      </if>
      <if test="adjustType != null">
        t.adjust_type = #{adjustType},
      </if>
      <if test="amount != null">
        t.amount = #{amount},
      </if>
      <if test="sort != null">
        t.sort = #{sort},
      </if>
      <if test="planQuantity != null">
        t.plan_quantity = #{planQuantity},
      </if>
      <if test="actualQuantity != null">
        t.actual_quantity = #{actualQuantity},
      </if>
      <if test="lockQuantity != null">
        t.lock_quantity = #{lockQuantity},
      </if>
      <if test="accountLimit != null">
        t.account_limit = #{accountLimit},
      </if>
      <if test="limitQuantity != null">
        t.limit_quantity = #{limitQuantity},
      </if>
      <if test="minSaleNum != null">
        t.min_sale_num = #{minSaleNum},
      </if>
      <if test="singleDeposit != null">
        t.single_deposit = #{singleDeposit},
      </if>
      <if test="expansionRatio != null">
        t.expansion_ratio = #{expansionRatio},
      </if>
      <if test="hidePrice != null">
        t.hide_price = #{hidePrice},
      </if>
      <if test="timingConfig != null">
        t.timing_config = #{timingConfig},
      </if>
      <if test="delFlag != null">
        t.del_flag = #{delFlag},
      </if>
      <if test="createTime != null">
        t.create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        t.update_time = #{updateTime},
      </if>
      <if test="isSupportTiming != null">
        t.is_support_timing = #{isSupportTiming},
      </if>
      <if test="autoPrice != null">
        t.auto_price = #{autoPrice},
      </if>
      <if test="discountLabel != null">
        t.discount_label = #{discountLabel},
      </if>
      <if test="ladderConfig != null">
        t.ladder_config = #{ladderConfig},
      </if>
      <if test="marketingCostSharingRatio != null">
        t.marketing_cost_sharing_ratio = #{marketingCostSharingRatio},
      </if>
    </trim>
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_sku_detail
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuDetail"
    useGeneratedKeys="true" keyProperty="id">
    insert into activity_sku_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="itemConfigId != null">
        `item_config_id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="roundingMode != null">
        `rounding_mode`,
      </if>
      <if test="adjustType != null">
        `adjust_type`,
      </if>
      <if test="amount != null">
        `amount`,
      </if>
      <if test="sort != null">
        `sort`,
      </if>
      <if test="planQuantity != null">
        `plan_quantity`,
      </if>
      <if test="actualQuantity != null">
        `actual_quantity`,
      </if>
      <if test="lockQuantity != null">
        `lock_quantity`,
      </if>
      <if test="accountLimit != null">
        `account_limit`,
      </if>
      <if test="limitQuantity != null">
        `limit_quantity`,
      </if>
      <if test="minSaleNum != null">
        `min_sale_num`,
      </if>
      <if test="singleDeposit != null">
        `single_deposit`,
      </if>
      <if test="expansionRatio != null">
        `expansion_ratio`,
      </if>
      <if test="hidePrice != null">
        `hide_price`,
      </if>
      <if test="timingConfig != null">
        `timing_config`,
      </if>
      <if test="isSupportTiming != null">
        `is_support_timing`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="autoPrice != null">
        `auto_price`,
      </if>
      <if test="discountLabel != null">
        `discount_label`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="ladderConfig != null">
        ladder_config,
      </if>
      <if test="marketingCostSharingRatio != null">
        marketing_cost_sharing_ratio,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="itemConfigId != null">
        #{itemConfigId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="roundingMode != null">
        #{roundingMode,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="planQuantity != null">
        #{planQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualQuantity != null">
        #{actualQuantity,jdbcType=INTEGER},
      </if>
      <if test="lockQuantity != null">
        #{lockQuantity,jdbcType=INTEGER},
      </if>
      <if test="accountLimit != null">
        #{accountLimit,jdbcType=TINYINT},
      </if>
      <if test="limitQuantity != null">
        #{limitQuantity,jdbcType=INTEGER},
      </if>
      <if test="minSaleNum != null">
        #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="singleDeposit != null">
        #{singleDeposit,jdbcType=DECIMAL},
      </if>
      <if test="expansionRatio != null">
        #{expansionRatio,jdbcType=DECIMAL},
      </if>
      <if test="hidePrice != null">
        #{hidePrice,jdbcType=TINYINT},
      </if>
      <if test="timingConfig != null">
        #{timingConfig,jdbcType=VARCHAR},
      </if>
      <if test="isSupportTiming != null">
        #{isSupportTiming,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="autoPrice != null">
        #{autoPrice,jdbcType=INTEGER},
      </if>
      <if test="discountLabel != null">
        #{discountLabel,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ladderConfig != null">
        #{ladderConfig,jdbcType=VARCHAR},
      </if>
      <if test="marketingCostSharingRatio != null">
        #{marketingCostSharingRatio,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuDetail">
    update activity_sku_detail
    <set>
      <if test="itemConfigId != null">
        `item_config_id` = #{itemConfigId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="roundingMode != null">
        rounding_mode = #{roundingMode,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        `adjust_type` = #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        `amount` = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="sort != null">
        `sort` = #{sort,jdbcType=INTEGER},
      </if>
      <if test="planQuantity != null">
        `plan_quantity` = #{planQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualQuantity != null">
        `actual_quantity` = #{actualQuantity,jdbcType=INTEGER},
      </if>
      <if test="lockQuantity != null">
        `lock_quantity` = #{lockQuantity,jdbcType=INTEGER},
      </if>
      <if test="accountLimit != null">
        `account_limit` = #{accountLimit,jdbcType=TINYINT},
      </if>
      <if test="limitQuantity != null">
        `limit_quantity` = #{limitQuantity,jdbcType=INTEGER},
      </if>
      <if test="minSaleNum != null">
        `min_sale_num` = #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="singleDeposit != null">
        `single_deposit` = #{singleDeposit,jdbcType=DECIMAL},
      </if>
      <if test="expansionRatio != null">
        `expansion_ratio` = #{expansionRatio,jdbcType=DECIMAL},
      </if>
      <if test="hidePrice != null">
        `hide_price` = #{hidePrice,jdbcType=TINYINT},
      </if>
      <if test="timingConfig != null">
        `timing_config` = #{timingConfig,jdbcType=VARCHAR},
      </if>
      <if test="isSupportTiming != null">
        `is_support_timing` = #{isSupportTiming,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="autoPrice != null">
        `auto_price` = #{autoPrice},
      </if>
      <if test="discountLabel != null">
        `discount_label` = #{discountLabel,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ladderConfig != null">
        ladder_config = #{ladderConfig},
      </if>
      <if test="marketingCostSharingRatio != null">
        marketing_cost_sharing_ratio = #{marketingCostSharingRatio},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySkuDetail" useGeneratedKeys="true" keyProperty="list.id">
    insert into activity_sku_detail (`item_config_id`, `sku`,rounding_mode,
    `adjust_type`, `amount`,
    `plan_quantity`, `actual_quantity`,
    `account_limit`, `limit_quantity`, `min_sale_num`,
    `single_deposit`, `expansion_ratio`, `hide_price`,
    `timing_config`,`is_support_timing`, `discount_label`, `ladder_config`, marketing_cost_sharing_ratio)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{itemConfigId,jdbcType=BIGINT}, #{item.sku,jdbcType=VARCHAR},#{item.roundingMode,jdbcType=TINYINT},
      #{item.adjustType,jdbcType=TINYINT}, #{item.amount,jdbcType=DECIMAL},
      #{item.planQuantity,jdbcType=INTEGER}, #{item.actualQuantity,jdbcType=INTEGER},
      #{item.accountLimit,jdbcType=TINYINT}, #{item.limitQuantity,jdbcType=INTEGER},
      #{item.minSaleNum,jdbcType=INTEGER},
      #{item.singleDeposit,jdbcType=DECIMAL}, #{item.expansionRatio,jdbcType=DECIMAL},
      #{item.hidePrice,jdbcType=TINYINT},
      #{item.timingConfig,jdbcType=VARCHAR}, #{item.isSupportTiming,jdbcType=TINYINT},
      #{item.discountLabel,jdbcType=TINYINT}, #{item.ladderConfig,jdbcType=VARCHAR}, #{item.marketingCostSharingRatio,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectBySku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where item_config_id = #{itemConfigId} and sku = #{sku} and del_flag = 0
  </select>

  <select id="selectByItemConfig" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where item_config_id = #{itemConfigId} and del_flag = 0
  </select>

  <select id="countByItemConfig" resultType="int">
    select
    count(*)
    from activity_sku_detail
    where item_config_id = #{itemConfigId} and del_flag = 0
  </select>

  <update id="updateDelFlag">
    update activity_sku_detail
    set del_flag = 1
    where item_config_id = #{itemConfigId}
      <if test="sku != null">
        and sku = #{sku}
      </if>
      and del_flag = 0
  </update>

  <select id="listByItemConfigs" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where sku = #{sku} and del_flag = 0
    and item_config_id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by item_config_id desc
  </select>

  <select id="listByItemConfigsSkus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where del_flag = 0
    and sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
    and item_config_id in
    <foreach collection="configList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <update id="updateDelFlagBatch">
    update activity_sku_detail
    set del_flag = 1
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and del_flag = 0
  </update>

  <delete id="deleteSkuByConfigId">
    delete from activity_sku_detail where  sku = #{sku} and item_config_id = #{itemConfigId}
  </delete>

  <select id="listByBasicInfoIds" resultMap="BaseResultMap">
    select asd.*
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_sku_detail asd on aic.id = asd.item_config_id
    where
        abi.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
        and sku = #{sku}
        and abi.del_flag = 0 and aic.del_flag = 0 and asd.del_flag = 0
  </select>

  <select id="listSkuByItemConfigIdStrings" resultType="string">
    select distinct sku
    from activity_sku_detail
    where
    item_config_id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and del_flag = 0
  </select>

  <update id="updateBatchByItemConfigId">
    update activity_sku_detail
    set del_flag = 1
    where item_config_id = #{itemConfigId} and del_flag = 0
    and sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
  </update>

  <!-- 查询列表可以根据分页进行查询 -->
  <select id="getPage" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuDetailQueryParam"  resultType="net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity" >
    SELECT
    t.id id,
    t.item_config_id itemConfigId,
    t.sku sku,
    t.rounding_mode roundingMode,
    t.adjust_type adjustType,
    t.amount amount,
    t.sort sort,
    t.plan_quantity planQuantity,
    t.actual_quantity actualQuantity,
    t.lock_quantity lockQuantity,
    t.account_limit accountLimit,
    t.limit_quantity limitQuantity,
    t.min_sale_num minSaleNum,
    t.single_deposit singleDeposit,
    t.expansion_ratio expansionRatio,
    t.hide_price hidePrice,
    t.timing_config timingConfig,
    t.del_flag delFlag,
    t.create_time createTime,
    t.update_time updateTime,
    t.is_support_timing isSupportTiming,
    t.auto_price autoPrice,
    t.discount_label discountLabel,
    t.ladder_config ladderConfig,
    t.marketing_cost_sharing_ratio marketingCostSharingRatio
    FROM activity_sku_detail t
    <include refid="whereColumnBySelect" />
    ORDER BY t.id DESC
  </select>

  <!-- 根据条件查询对象 -->
  <select id="selectByCondition" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuDetailQueryParam" resultMap="BaseResultMap" >
    SELECT <include refid="activitySkuDetailColumns" />
    FROM activity_sku_detail t
    <include refid="whereColumnBySelect"></include>
  </select>
  <select id="listBySkusAndOwnerId" resultType="net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity">
    select asd.sku, abi.id basicInfoId, abi.name activityName
    from activity_sku_detail asd
    left join activity_item_config aic on asd.item_config_id = aic.id
    left join activity_basic_info abi on aic.basic_info_id = abi.id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    where asd.sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
    and abi.owner_id = #{ownerId}
    and abi.del_flag = 0
    and abi.audit_status in (0, 1)
    and abi.status in (-1, 1)
    and asd.del_flag = 0
    and abi.system_origin = #{systemOrigin}
    and abi.type = #{type}
    and (abi.start_time &lt;= #{endTime} and abi.end_time &gt;= #{startTime}
    or abi.is_permanent = 1)
    <if test="scopeList != null and scopeList.size > 0">
      and (asco.scope_id, asco.scope_type) in
      <foreach collection="scopeList" item="item" open="(" separator="," close=")">
        (#{item.scopeId},#{item.scopeType})
      </foreach>
    </if>
  </select>

</mapper>
