<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.marketing.center.infrastructure.activity.mapper.ActivitySceneConfigMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="basic_info_id" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="platform" jdbcType="TINYINT" property="platform"/>
    <result column="place" jdbcType="TINYINT" property="place"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `basic_info_id`, `platform`, `place`, `del_flag`, `create_time`, `update_time`
  </sql>
  <!-- 列定义 -->
  <sql id="activitySceneConfigColumns">
    t.id,
          t.basic_info_id,
          t.platform,
          t.place,
          t.del_flag,
          t.create_time,
          t.update_time
  </sql>

  <!-- 查询条件SQL -->
  <sql id="whereColumnBySelect">
    <trim prefix="WHERE" prefixOverrides="AND | OR">
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="basicInfoId != null">
        AND t.basic_info_id = #{basicInfoId}
      </if>
      <if test="platform != null">
        AND t.platform = #{platform}
      </if>
      <if test="place != null">
        AND t.place = #{place}
      </if>
      <if test="delFlag != null">
        AND t.del_flag = #{delFlag}
      </if>
      <if test="createTime != null">
        AND t.create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        AND t.update_time = #{updateTime}
      </if>
    </trim>
  </sql>

  <!-- 修改字段SQL -->
  <sql id="whereColumnByUpdate">
    <trim prefix="SET" suffixOverrides=",">
      <if test="basicInfoId != null">
        t.basic_info_id = #{basicInfoId},
      </if>
      <if test="platform != null">
        t.platform = #{platform},
      </if>
      <if test="place != null">
        t.place = #{place},
      </if>
      <if test="delFlag != null">
        t.del_flag = #{delFlag},
      </if>
      <if test="createTime != null">
        t.create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        t.update_time = #{updateTime},
      </if>
    </trim>
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_scene_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_scene_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig">
    insert into activity_scene_config (`id`, `basic_info_id`, `platform`,
                                       `place`, `del_flag`, `create_time`,
                                       `update_time`)
    values (#{id,jdbcType=BIGINT}, #{basicInfoId,jdbcType=BIGINT}, #{platform,jdbcType=TINYINT},
            #{place,jdbcType=TINYINT}, #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig">
    insert into activity_scene_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="basicInfoId != null">
        `basic_info_id`,
      </if>
      <if test="platform != null">
        `platform`,
      </if>
      <if test="place != null">
        `place`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="basicInfoId != null">
        #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=TINYINT},
      </if>
      <if test="place != null">
        #{place,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig">
    update activity_scene_config
    <set>
      <if test="basicInfoId != null">
        `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        `platform` = #{platform,jdbcType=TINYINT},
      </if>
      <if test="place != null">
        `place` = #{place,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivitySceneConfig">
    update activity_scene_config
    set `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
        `platform`      = #{platform,jdbcType=TINYINT},
        `place`         = #{place,jdbcType=TINYINT},
        `del_flag`      = #{delFlag,jdbcType=TINYINT},
        `create_time`   = #{createTime,jdbcType=TIMESTAMP},
        `update_time`   = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

    <update id="updateDelFlag">
      update activity_scene_config
      set del_flag = 1
      where basic_info_id = #{basicInfoId} and del_flag = 0
    </update>

    <select id="selectByInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_scene_config
    where basic_info_id = #{basicInfoId} and del_flag = 0
  </select>

  <!-- 查询列表可以根据分页进行查询 -->
  <select id="getPage" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivitySceneConfigQueryParam"  resultType="net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity" >
    SELECT
    t.id id,
    t.basic_info_id basicInfoId,
    t.platform platform,
    t.place place,
    t.del_flag delFlag,
    t.create_time createTime,
    t.update_time updateTime
    FROM activity_scene_config t
    <include refid="whereColumnBySelect" />
    ORDER BY t.id DESC
  </select>

  <!-- 根据条件查询对象 -->
  <select id="selectByCondition" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivitySceneConfigQueryParam" resultMap="BaseResultMap" >
    SELECT <include refid="activitySceneConfigColumns" />
    FROM activity_scene_config t
    <include refid="whereColumnBySelect"></include>
  </select>
</mapper>