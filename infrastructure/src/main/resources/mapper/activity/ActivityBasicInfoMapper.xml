<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.marketing.center.infrastructure.activity.mapper.ActivityBasicInfoMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="is_permanent" jdbcType="TINYINT" property="isPermanent"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="need_pre" jdbcType="TINYINT" property="needPre"/>
    <result column="pre_start_time" jdbcType="TIMESTAMP" property="preStartTime"/>
    <result column="pre_end_time" jdbcType="TIMESTAMP" property="preEndTime"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="tag" jdbcType="TINYINT" property="tag"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
    <result column="system_origin" jdbcType="INTEGER" property="systemOrigin"/>
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus"/>
    <result column="audit_user_id" jdbcType="BIGINT" property="auditUserId"/>
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark"/>
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
  </resultMap>

  <resultMap id="ResultMapForPage" type="net.xianmu.marketing.center.domain.activity.entity.ActivityPageEntity">
    <id column="id" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="is_permanent" jdbcType="TINYINT" property="isPermanent"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="tag" jdbcType="TINYINT" property="tag"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>


  <resultMap id="ResultPageRespDTOMapForPage" type="net.xianmu.marketing.center.domain.activity.dto.ActivityPageRespDTO">
    <id column="id" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="is_permanent" jdbcType="TINYINT" property="isPermanent"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="tag" jdbcType="TINYINT" property="tag"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `name`, `start_time`, `end_time`, `is_permanent`, `status`, `need_pre`, `pre_start_time`,
    `pre_end_time`, `type`, `tag`, `remark`, `creator_id`, `updater_id`, `del_flag`,
    `create_time`, `update_time`, owner_id, system_origin, audit_status, audit_user_id, audit_remark, audit_time
  </sql>

  <!-- 查询条件SQL -->
  <sql id="whereColumnBySelect">
    <trim prefix="WHERE" prefixOverrides="AND | OR">
      <if test="id != null">
        AND id = #{id}
      </if>
      <if test="name != null and name !=''">
        AND `name` = #{name}
      </if>
      <if test="startTime != null">
        AND start_time = #{startTime}
      </if>
      <if test="endTime != null">
        AND end_time = #{endTime}
      </if>
      <if test="isPermanent != null">
        AND is_permanent = #{isPermanent}
      </if>
      <if test="status != null">
        AND status = #{status}
      </if>
      <if test="needPre != null">
        AND need_pre = #{needPre}
      </if>
      <if test="preStartTime != null">
        AND pre_start_time = #{preStartTime}
      </if>
      <if test="preEndTime != null">
        AND pre_end_time = #{preEndTime}
      </if>
      <if test="type != null">
        AND `type` = #{type}
      </if>
      <if test="tag != null">
        AND tag = #{tag}
      </if>
      <if test="remark != null and remark !=''">
        AND remark = #{remark}
      </if>
      <if test="creatorId != null">
        AND creator_id = #{creatorId}
      </if>
      <if test="updaterId != null">
        AND updater_id = #{updaterId}
      </if>
      <if test="delFlag != null">
        AND del_flag = #{delFlag}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime}
      </if>
      <if test="ownerId != null">
        AND owner_id = #{ownerId}
      </if>
      <if test="systemOrigin != null">
        AND system_origin = #{systemOrigin}
      </if>
      <if test="auditStatus != null">
        AND audit_status = #{auditStatus}
      </if>
      <if test="auditUserId != null">
        AND audit_user_id = #{auditUserId}
      </if>
      <if test="auditRemark != null and auditRemark !=''">
        AND audit_remark = #{auditRemark}
      </if>
    </trim>
  </sql>

  <!-- 修改字段SQL -->
  <sql id="whereColumnByUpdate">
    <trim prefix="SET" suffixOverrides=",">
      <if test="name != null">
        t.name = #{name},
      </if>
      <if test="startTime != null">
        t.start_time = #{startTime},
      </if>
      <if test="endTime != null">
        t.end_time = #{endTime},
      </if>
      <if test="isPermanent != null">
        t.is_permanent = #{isPermanent},
      </if>
      <if test="status != null">
        t.status = #{status},
      </if>
      <if test="needPre != null">
        t.need_pre = #{needPre},
      </if>
      <if test="preStartTime != null">
        t.pre_start_time = #{preStartTime},
      </if>
      <if test="preEndTime != null">
        t.pre_end_time = #{preEndTime},
      </if>
      <if test="type != null">
        t.type = #{type},
      </if>
      <if test="tag != null">
        t.tag = #{tag},
      </if>
      <if test="remark != null">
        t.remark = #{remark},
      </if>
      <if test="creatorId != null">
        t.creator_id = #{creatorId},
      </if>
      <if test="updaterId != null">
        t.updater_id = #{updaterId},
      </if>
      <if test="delFlag != null">
        t.del_flag = #{delFlag},
      </if>
      <if test="createTime != null">
        t.create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        t.update_time = #{updateTime},
      </if>
      <if test="ownerId != null">
        t.owner_id = #{ownerId},
      </if>
      <if test="systemOrigin != null">
        t.system_origin = #{systemOrigin},
      </if>
      <if test="auditStatus != null">
        t.audit_status = #{auditStatus},
      </if>
      <if test="auditUserId != null">
        t.audit_user_id = #{auditUserId},
      </if>
      <if test="auditRemark != null">
        t.audit_remark = #{auditRemark},
      </if>
    </trim>
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_basic_info
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo">
    insert into activity_basic_info (`id`, `name`, `start_time`,
                                     `end_time`, `is_permanent`, `status`,
                                     `need_pre`, `pre_start_time`, `pre_end_time`,
                                     `type`, `tag`, `remark`,
                                     `creator_id`, `updater_id`, `del_flag`,
                                     `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP}, #{isPermanent,jdbcType=TINYINT},
            #{status,jdbcType=TINYINT},
            #{needPre,jdbcType=TINYINT}, #{preStartTime,jdbcType=TIMESTAMP},
            #{preEndTime,jdbcType=TIMESTAMP},
            #{type,jdbcType=INTEGER}, #{tag,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
            #{creatorId,jdbcType=INTEGER}, #{updaterId,jdbcType=INTEGER},
            #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo"
    useGeneratedKeys="true" keyProperty="id">
    insert into activity_basic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="startTime != null">
        `start_time`,
      </if>
      <if test="endTime != null">
        `end_time`,
      </if>
      <if test="isPermanent != null">
        `is_permanent`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="needPre != null">
        `need_pre`,
      </if>
      <if test="preStartTime != null">
        `pre_start_time`,
      </if>
      <if test="preEndTime != null">
        `pre_end_time`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="tag != null">
        `tag`,
      </if>
      <if test="remark != null">
        `remark`,
      </if>
      <if test="creatorId != null">
        `creator_id`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="ownerId != null">
        owner_id,
      </if>
      <if test="systemOrigin != null">
        system_origin,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditUserId != null">
        audit_user_id,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPermanent != null">
        #{isPermanent,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="needPre != null">
        #{needPre,jdbcType=TINYINT},
      </if>
      <if test="preStartTime != null">
        #{preStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preEndTime != null">
        #{preEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerId != null">
        #{ownerId,jdbcType=NUMERIC},
      </if>
      <if test="systemOrigin != null">
        #{systemOrigin,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditUserId != null">
        #{auditUserId,jdbcType=NUMERIC},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo">
    update activity_basic_info
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        `start_time` = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        `end_time` = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPermanent != null">
        `is_permanent` = #{isPermanent,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="needPre != null">
        `need_pre` = #{needPre,jdbcType=TINYINT},
      </if>
      <if test="preStartTime != null">
        `pre_start_time` = #{preStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preEndTime != null">
        `pre_end_time` = #{preEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        `tag` = #{tag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        `remark` = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        `creator_id` = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerId != null">
        owner_id = #{ownerId},
      </if>
      <if test="systemOrigin != null">
        system_origin = #{systemOrigin},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo">
    update activity_basic_info
    set `name`           = #{name,jdbcType=VARCHAR},
        `start_time`     = #{startTime,jdbcType=TIMESTAMP},
        `end_time`       = #{endTime,jdbcType=TIMESTAMP},
        `is_permanent`   = #{isPermanent,jdbcType=TINYINT},
        `status`         = #{status,jdbcType=TINYINT},
        `need_pre`       = #{needPre,jdbcType=TINYINT},
        `pre_start_time` = #{preStartTime,jdbcType=TIMESTAMP},
        `pre_end_time`   = #{preEndTime,jdbcType=TIMESTAMP},
        `type`           = #{type,jdbcType=INTEGER},
        `tag`            = #{tag,jdbcType=TINYINT},
        `remark`         = #{remark,jdbcType=VARCHAR},
        `creator_id`     = #{creatorId,jdbcType=INTEGER},
        `updater_id`     = #{updaterId,jdbcType=INTEGER},
        `del_flag`       = #{delFlag,jdbcType=TINYINT},
        `create_time`    = #{createTime,jdbcType=TIMESTAMP},
        `update_time`    = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByScope" parameterType="net.xianmu.marketing.center.domain.activity.param.ScopeQueryParam"
    resultType="net.xianmu.marketing.center.domain.activity.entity.ActivityItemScopeEntity">
    select abi.name, abi.id basicInfoId, aic.id itemConfigId, asco.scope_id scopeId, abi.start_time startTime,
           abi.end_time endTime, abi.is_permanent isPermanent, abi.status status
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    where
    abi.type = #{type}
    <if test="id != null">
      and abi.id != #{id}
    </if>
    <if test="endTime != null">
      and (abi.start_time &lt;= #{endTime} and abi.end_time &gt;= #{startTime}
      or abi.is_permanent = 1)
    </if>
    <if test="endTime == null">
      and (abi.end_time &gt;= #{startTime}
      or abi.is_permanent = 1)
    </if>
    and abi.del_flag = 0 and asco.del_flag = 0
    <if test="scopeList != null and scopeList.size > 0">
      and (scope_id,scope_type) in
      <foreach collection="scopeList" item="item" open="(" separator="," close=")">
        (#{item.scopeId},#{item.scopeType})
      </foreach>
    </if>
    <if test="ownerId != null">
      and abi.owner_id = #{ownerId}
      and abi.audit_status in (0, 1)
      and abi.status in (-1, 1)
    </if>
    <if test="systemOrigin != null">
      and abi.system_origin = #{systemOrigin}
    </if>
  </select>


  <select id="listByScope"
    resultType="net.xianmu.marketing.center.domain.activity.entity.ActivityItemScopeEntity">
    select aic.basic_info_id basicInfoId,aic.id itemConfigId, asco.scope_id scopeId
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      <if test="type != null">
        and abi.type = #{type}
      </if>
      <if test="list != null and list.size > 0">
        and (scope_id,scope_type) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          (#{item.scopeId},#{item.scopeType})
        </foreach>
      </if>
      <if test="activityStatus != null">
        <choose>
          <when test="activityStatus == 0">
            and (abi.start_time &lt; now()
            or (abi.start_time &lt;= now() and abi.end_time &gt;= now() and status = 0)
            or (abi.is_permanent = 1 and status = 0))
          </when>
          <when test="activityStatus == 1">
            and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
            or abi.is_permanent = 1)
            and status = 1
          </when>
          <when test="activityStatus == 2">
            and abi.end_time &lt; now()
          </when>
        </choose>
      </if>
    </where>
    order by abi.id desc
  </select>

  <select id="getEffectingById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info
    where id = #{id} and ((start_time &lt;= now() and end_time &gt;= now())
    or is_permanent = 1)
    and status = 1
  </select>

  <select id="listValidByScope"
    resultType="net.xianmu.marketing.center.domain.activity.entity.ActivityItemScopeEntity">
    select aic.basic_info_id basicInfoId,aic.id itemConfigId, asco.scope_id scopeId
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0 and (abi.end_time &gt; now() ||
      abi.is_permanent = 1)
      <if test="type != null">
        and abi.type = #{type}
      </if>
      <if test="list != null and list.size > 0">
        and (scope_id,scope_type) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          (#{item.scopeId},#{item.scopeType})
        </foreach>
      </if>
    </where>
    order by abi.id desc
  </select>

  <select id="selectUnderwayByEntity" parameterType="net.xianmu.marketing.center.infrastructure.activity.model.ActivityBasicInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info
    where end_time > now() and now() >= start_time
      <if test="status !=null">
        and status = #{status}
      </if>
      <if test="type !=null">
        and `type` = #{type}
      </if>
        <if test="tag !=null">
            and `tag` = #{tag}
        </if>
      <if test="remark !=null">
        and remark = #{remark}
      </if>
      <if test="delFlag !=null">
        and del_flag = #{delFlag}
      </if>
  </select>

  <!-- 查询列表可以根据分页进行查询 -->
  <select id="getPage" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam"  resultType="net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity" >
    SELECT
    t.id id,
    t.name name,
    t.start_time startTime,
    t.end_time endTime,
    t.is_permanent isPermanent,
    t.status status,
    t.need_pre needPre,
    t.pre_start_time preStartTime,
    t.pre_end_time preEndTime,
    t.type type,
    t.tag tag,
    t.remark remark,
    t.creator_id creatorId,
    t.updater_id updaterId,
    t.del_flag delFlag,
    t.create_time createTime,
    t.update_time updateTime,
    t.owner_id ownerId,
    t.system_origin systemOrigin,
    t.audit_status auditStatus,
    t.audit_user_id auditUserId,
    t.audit_remark auditRemark
    FROM activity_basic_info t
    <include refid="whereColumnBySelect" />
    ORDER BY t.id DESC
  </select>


  <!-- 根据条件查询对象 -->
  <select id="selectByCondition" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam" resultMap="BaseResultMap" >
    SELECT <include refid="Base_Column_List" />
    FROM activity_basic_info
    <include refid="whereColumnBySelect"></include>
  </select>


  <select id="listByQuery" parameterType="net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam"
          resultMap="ResultPageRespDTOMapForPage">
    select distinct abi.*
    from activity_basic_info abi
    <if test="sku != null">
      left join activity_item_config aic on abi.id = aic.basic_info_id
      left join activity_sku_detail asd on aic.id = asd.item_config_id
    </if>
    <if test="scopeList != null and scopeList.size > 0">
      left join activity_scope_config asco on abi.id = asco.basic_info_id
    </if>
    <where>
      abi.del_flag = 0
      <if test="id != null">
        and abi.id = #{id}
      </if>
      <if test="type != null">
        and abi.type = #{type}
      </if>
      <if test="name != null">
        and abi.name like CONCAT('%', #{name},'%')
      </if>
      <if test="sku != null">
        and sku = #{sku} and asd.del_flag = 0
      </if>
      <if test="scopeList != null and scopeList.size > 0">
        and asco.del_flag = 0
        and (scope_id,scope_type) in
        <foreach collection="scopeList" item="scope" open="(" separator="," close=")">
          (#{scope.scopeId}, #{scope.scopeType})
        </foreach>
      </if>
      <if test="activityStatus != null">
        <choose>
          <when test="activityStatus == 0">
            and (abi.start_time &gt; now()
            or (abi.start_time &lt;= now() and abi.end_time &gt;= now() and status = 0)
            or (abi.is_permanent = 1 and status = 0))
          </when>
          <when test="activityStatus == 1">
            and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
            or abi.is_permanent = 1)
            and status = 1
          </when>
          <when test="activityStatus == 2">
            and abi.end_time &lt; now()
          </when>
        </choose>
      </if>
      <if test="creatorId != null">
        and abi.creator_id = #{creatorId}
      </if>
    </where>
    order by abi.id desc
  </select>

</mapper>
