#set( $symbol_pound = '#' )
#set( $symbol_dollar = '$' )
#set( $symbol_escape = '\' )
server:
  port: 80
  servlet:
    context-path: /marketing-center
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ******************************************************************************************
    username: test
    password: xianmu619
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 1
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5    #dev1:0  dev2:1  dev3:2 dev4:4  qa:5

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: 34792f7a-aaa2-41ee-8a7f-53be483c2533
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000
xm:
  oss:
    # 生产测试不一样
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net
  log:
    enable: true
    resp: true
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752

rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_mall
    secret-key: ''
    sendMsgTimeout: 10000

wechat:
  app:
    id: wx32a0e329197b752b
    secret: e3d4592bb9f437e682334efd37928ee8
  mp-app:
    id: wx674b60a859676717
    secret: 250fd1499c812616fe4b0caab9dffb0f

xianmu:
  mall:
    domain: https://qah5.summerfarm.net