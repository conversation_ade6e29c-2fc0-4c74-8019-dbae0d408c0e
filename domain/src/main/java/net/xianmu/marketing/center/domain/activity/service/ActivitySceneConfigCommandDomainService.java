package net.xianmu.marketing.center.domain.activity.service;



import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySceneConfigCommandParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySceneConfigCommandRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySceneConfigQueryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 活动场景配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivitySceneConfigCommandDomainService {


    @Autowired
    private ActivitySceneConfigCommandRepository activitySceneConfigCommandRepository;
    @Autowired
    private ActivitySceneConfigQueryRepository activitySceneConfigQueryRepository;



    public ActivitySceneConfigEntity insert(ActivitySceneConfigCommandParam param) {
        return activitySceneConfigCommandRepository.insertSelective(param);
    }


    public int update(ActivitySceneConfigCommandParam param) {
        return activitySceneConfigCommandRepository.updateSelectiveById(param);
    }

    public void updateDelFlag(Long basicInfoId) {
        activitySceneConfigCommandRepository.updateDelFlag(basicInfoId);
    }
}
