package net.xianmu.marketing.center.domain.activity.repository;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuPriceEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuPriceCommandParam;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivitySkuPriceCommandRepository {

    int deleteBySkus(Long basicInfoId, List<String> skus);

    ActivitySkuPriceEntity insertSelective(ActivitySkuPriceCommandParam param);

    int updateSelectiveById(ActivitySkuPriceCommandParam param);

    void deleteByInfoId(Long basicInfoId);
}
