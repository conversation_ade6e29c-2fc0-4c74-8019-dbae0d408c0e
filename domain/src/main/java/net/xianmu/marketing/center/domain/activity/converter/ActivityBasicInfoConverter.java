package net.xianmu.marketing.center.domain.activity.converter;

import net.xianmu.marketing.center.domain.activity.dto.ActivityBasicInfoDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;

/**
 * <AUTHOR>
 * @Date 2025/6/18 13:47
 * @PackageName:net.xianmu.marketing.center.domain.activity.converter
 * @ClassName: ActivityBasicInfoConverter
 * @Description: TODO
 * @Version 1.0
 */
public class ActivityBasicInfoConverter {
    public static ActivityBasicInfoDTO toActivityBasicInfoDTO(ActivityBasicInfoEntity basicInfoEntity) {
        if (basicInfoEntity == null) {
            return null;
        }
        ActivityBasicInfoDTO activityBasicInfoDTO = new ActivityBasicInfoDTO();
        activityBasicInfoDTO.setBasicInfoId(basicInfoEntity.getId());
        activityBasicInfoDTO.setName(basicInfoEntity.getName());
        activityBasicInfoDTO.setStartTime(basicInfoEntity.getStartTime());
        activityBasicInfoDTO.setEndTime(basicInfoEntity.getEndTime());
        activityBasicInfoDTO.setIsPermanent(basicInfoEntity.getIsPermanent());
        activityBasicInfoDTO.setStatus(basicInfoEntity.getStatus());
        activityBasicInfoDTO.setNeedPre(basicInfoEntity.getNeedPre());
        activityBasicInfoDTO.setPreStartTime(basicInfoEntity.getPreStartTime());
        activityBasicInfoDTO.setPreEndTime(basicInfoEntity.getPreEndTime());
        activityBasicInfoDTO.setType(basicInfoEntity.getType());
        activityBasicInfoDTO.setTag(basicInfoEntity.getTag());
        activityBasicInfoDTO.setRemark(basicInfoEntity.getRemark());
        activityBasicInfoDTO.setCreatorId(basicInfoEntity.getCreatorId());
        activityBasicInfoDTO.setUpdaterId(basicInfoEntity.getUpdaterId());
        activityBasicInfoDTO.setDelFlag(basicInfoEntity.getDelFlag());
        activityBasicInfoDTO.setCreateTime(basicInfoEntity.getCreateTime());
        activityBasicInfoDTO.setUpdateTime(basicInfoEntity.getUpdateTime());
        activityBasicInfoDTO.setOwnerId(basicInfoEntity.getOwnerId());
        activityBasicInfoDTO.setSystemOrigin(basicInfoEntity.getSystemOrigin());
        activityBasicInfoDTO.setAuditStatus(basicInfoEntity.getAuditStatus());
        activityBasicInfoDTO.setAuditUserId(basicInfoEntity.getAuditUserId());
        activityBasicInfoDTO.setAuditRemark(basicInfoEntity.getAuditRemark());
        activityBasicInfoDTO.setAuditTime(basicInfoEntity.getAuditTime());
        return activityBasicInfoDTO;
    }
}
