package net.xianmu.marketing.center.domain.activity.service;


import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuPriceEntity;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuPriceQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuPriceCommandRepository;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuPriceCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;


/**
 *
 * @Title: 城市活动sku价格信息表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivitySkuPriceCommandDomainService {


    @Autowired
    private ActivitySkuPriceCommandRepository activitySkuPriceCommandRepository;
    @Autowired
    private ActivitySkuPriceQueryRepository activitySkuPriceQueryRepository;



    public ActivitySkuPriceEntity insert(ActivitySkuPriceCommandParam param) {
        return activitySkuPriceCommandRepository.insertSelective(param);
    }


    public int update(ActivitySkuPriceCommandParam param) {
        return activitySkuPriceCommandRepository.updateSelectiveById(param);
    }

    public void deleteSkuByInfoId(Long basicInfoId, String sku) {
        activitySkuPriceCommandRepository.deleteBySkus(basicInfoId, Collections.singletonList(sku));
    }

    public void deleteByInfoId(Long basicInfoId) {
        activitySkuPriceCommandRepository.deleteByInfoId(basicInfoId);
    }
}
