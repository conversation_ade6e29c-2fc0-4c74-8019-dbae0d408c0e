package net.xianmu.marketing.center.domain.activity.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuPriceEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuPriceQueryParam;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivitySkuPriceQueryRepository {

    PageInfo<ActivitySkuPriceEntity> getPage(ActivitySkuPriceQueryParam param);

    ActivitySkuPriceEntity selectById(Long id);

    List<ActivitySkuPriceEntity> selectByCondition(ActivitySkuPriceQueryParam param);
}
