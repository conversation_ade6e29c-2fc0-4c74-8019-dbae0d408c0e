package net.xianmu.marketing.center.domain.activity.repository;



import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySceneConfigCommandParam;




/**
*
* <AUTHOR>
* @date 2025-06-10 15:41:35
* @version 1.0
*
*/
public interface ActivitySceneConfigCommandRepository {

    ActivitySceneConfigEntity insertSelective(ActivitySceneConfigCommandParam param);

    int updateSelectiveById(ActivitySceneConfigCommandParam param);

    void updateDelFlag(Long basicInfoId);
}