package net.xianmu.marketing.center.domain.activity.service;


import cn.hutool.core.collection.CollectionUtil;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.marketing.center.common.enums.activity.GoodSelectWayEnum;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.converter.ActivityItemConfigConverter;
import net.xianmu.marketing.center.domain.activity.dto.ActivityItemConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuDetailQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivityItemConfigQueryRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.xianmu.marketing.center.facade.item.ItemQueryFacade;
import net.xianmu.marketing.center.facade.item.dto.SkuInfoDetailDTO;
import net.xianmu.marketing.center.facade.item.input.BatchSkuInput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *
 * @Title: 活动商品配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivityItemConfigQueryDomainService {

    @Resource
    private ActivityItemConfigQueryRepository itemConfigQueryRepository;

    @Resource
    private ActivitySkuDetailQueryRepository skuDetailQueryRepository;

    @Resource
    private ItemQueryFacade itemQueryFacade;




    /**
     * 构建商品配置信息
     *  @param basicInfoId
     * @param systemOrigin
     * @param needDetail
     */
    public ActivityItemConfigDTO buildItemConfig(Long basicInfoId, Integer systemOrigin, boolean needDetail) {
        ActivityItemConfigEntity itemConfig = itemConfigQueryRepository.selectByInfoId(basicInfoId);
        ActivityItemConfigDTO activityItemConfigDTO = ActivityItemConfigConverter.toActivityItemConfigDTO(itemConfig);
        if (activityItemConfigDTO == null) {
            return null;
        }

        Long itemConfigId = itemConfig.getId();
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), itemConfig.getGoodSelectWay())) {
            ActivitySkuDetailQueryParam queryParam = new ActivitySkuDetailQueryParam();
            queryParam.setItemConfigId(itemConfigId);

            //供应商可以查询已删除sku信息
            if (Objects.equals(systemOrigin, SystemOriginEnum.ADMIN.getType())) {
                queryParam.setDelFlag(CommonStatus.NO.getCode());
            }
            List<ActivitySkuDetailEntity> skuDetailList = skuDetailQueryRepository.selectByCondition(queryParam);
            if (CollectionUtil.isEmpty(skuDetailList)) {
                return activityItemConfigDTO;
            }

            activityItemConfigDTO.setSkuDetailList(skuDetailList);
            if (!needDetail) {
                return activityItemConfigDTO;
            }

            //组装商品信息数据,批量查询商品
            List<String> skus = skuDetailList.stream().map(x -> x.getSku()).collect(Collectors.toList());

            BatchSkuInput input = new BatchSkuInput();
            input.setSkus(skus);
            List<SkuInfoDetailDTO> skuBaseInfoDTOS = itemQueryFacade.listBySkusNew(input);
            Map<String, SkuInfoDetailDTO> skuBaseInfoDTOMap = skuBaseInfoDTOS.stream()
                    .collect(Collectors.toMap(x -> x.getSku(), Function.identity()));
            skuDetailList.stream().forEach(x -> {
                SkuInfoDetailDTO skuBaseInfoDTO = skuBaseInfoDTOMap.get(x.getSku());
                if (skuBaseInfoDTO != null) {
                    x.setSkuName(skuBaseInfoDTO.getPdName());
                    x.setWeight(skuBaseInfoDTO.getWeight());
                    x.setUnit(skuBaseInfoDTO.getUnit());
                    x.setLogo(skuBaseInfoDTO.getPicturePath());
                }
            });
        } else {
            //类目或者标签处理

        }
        return activityItemConfigDTO;
    }
}
