package net.xianmu.marketing.center.domain.activity.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/12/26
 */
@Data
public class ActivityItemScopeEntity implements Serializable {

    /**
     * 活动基本信息id
     */
    private Long basicInfoId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 商品配置id
     */
    private Long itemConfigId;

    /**
     * 活动范围id
     */
    private Long scopeId;

    /**
     *
     */
    private LocalDateTime startTime;

    /**
     *
     */
    private LocalDateTime endTime;

    /**
     * 是否永久
     */
    private Integer isPermanent;

    /**
     * 活动状态
     */
    private Integer status;
}
