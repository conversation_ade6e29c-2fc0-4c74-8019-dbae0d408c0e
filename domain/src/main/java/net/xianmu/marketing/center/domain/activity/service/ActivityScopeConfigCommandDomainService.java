package net.xianmu.marketing.center.domain.activity.service;


import com.google.common.collect.Lists;
import net.xianmu.marketing.center.common.enums.activity.ScopeTypeEnum;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityScopeConfigCommandParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityScopeConfigQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuScopeCommandRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuScopeQueryRepository;
import net.xianmu.marketing.center.domain.merchantpool.entity.MerchantPoolInfoEntity;
import net.xianmu.marketing.center.domain.merchantpool.param.query.MerchantPoolInfoQueryParam;
import net.xianmu.marketing.center.domain.merchantpool.repsitory.MerchantPoolInfoQueryRepository;
import net.xianmu.marketing.center.facade.area.AreaQueryFacade;
import net.xianmu.marketing.center.facade.area.dto.AreaSimpleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 *
 * @Title: 活动生效范围配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivityScopeConfigCommandDomainService {


    @Autowired
    private ActivitySkuScopeCommandRepository activityScopeConfigCommandRepository;
    @Autowired
    private ActivitySkuScopeQueryRepository activityScopeConfigQueryRepository;


    public ActivityScopeConfigEntity insert(ActivityScopeConfigCommandParam param) {
        return activityScopeConfigCommandRepository.insertSelective(param);
    }


    public int update(ActivityScopeConfigCommandParam param) {
        return activityScopeConfigCommandRepository.updateSelectiveById(param);
    }

    public List<ActivityScopeConfigEntity> insertBatch(List<ActivityScopeConfigCommandParam> scopeConfigParams) {
        return activityScopeConfigCommandRepository.insertBatch(scopeConfigParams);
    }


    public void updateDelFlag(Long basicInfoId, Long updaterId) {
        activityScopeConfigCommandRepository.updateDelFlag(basicInfoId, updaterId);
    }
}
