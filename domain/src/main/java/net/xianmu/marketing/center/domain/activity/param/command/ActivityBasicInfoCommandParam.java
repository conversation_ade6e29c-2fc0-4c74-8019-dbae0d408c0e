package net.xianmu.marketing.center.domain.activity.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Data
public class ActivityBasicInfoCommandParam {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 活动名称
	 */
	private String name;

	/**
	 * 活动开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 活动结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 是否永久，0 否，1 是
	 */
	private Integer isPermanent;

	/**
	 * 活动状态，0 暂停，1 开启（审核通过），-1审核中（待审核），100审核拒绝
	 */
	private Integer status;

	/**
	 * 是否需要预热，0 否，1 是
	 */
	private Integer needPre;

	/**
	 * 预热开始时间
	 */
	private LocalDateTime preStartTime;

	/**
	 * 预热结束时间
	 */
	private LocalDateTime preEndTime;

	/**
	 * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
	 */
	private Integer type;

	/**
	 * 活动目的(标签)，0 滞销促销，1 临保清仓，2 新品推广，3 用户召回，4 潜力品推广
	 */
	private Integer tag;

	/**
	 * 活动备注
	 */
	private String remark;

	/**
	 * 创建人id  auth_user.biz_user_id
	 */
	private Integer creatorId;

	/**
	 * 最后一次修改人id
	 */
	private Integer updaterId;

	/**
	 * 是否已被删除，0 否，1 是
	 */
	private Integer delFlag;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 归属人  平台默认为null 供应商supplier_id
	 */
	private Long ownerId;

	/**
	 * 创建人系统来源  0-srm, 2-admin
	 */
	private Integer systemOrigin;

	/**
	 * 审核状态 0-待审核  1-审核通过  2-审核拒绝
	 */
	private Integer auditStatus;

	/**
	 * 审核人ID
	 */
	private Long auditUserId;

	/**
	 * 审核备注
	 */
	private String auditRemark;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;
	
}