package net.xianmu.marketing.center.domain.activity.service;


import net.xianmu.marketing.center.domain.activity.repository.ActivityItemConfigQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivityItemConfigCommandRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityItemConfigCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 活动商品配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivityItemConfigCommandDomainService {


    @Autowired
    private ActivityItemConfigCommandRepository activityItemConfigCommandRepository;
    @Autowired
    private ActivityItemConfigQueryRepository activityItemConfigQueryRepository;



    public ActivityItemConfigEntity insert(ActivityItemConfigCommandParam param) {
        return activityItemConfigCommandRepository.insertSelective(param);
    }


    public int update(ActivityItemConfigCommandParam param) {
        return activityItemConfigCommandRepository.updateSelectiveById(param);
    }

    public Long updateDelFlag(ActivityItemConfigCommandParam itemConfig) {
        return activityItemConfigCommandRepository.updateDelFlag(itemConfig);
    }
}
