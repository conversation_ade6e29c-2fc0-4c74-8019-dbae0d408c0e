package net.xianmu.marketing.center.domain.activity.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityScopeConfigQueryParam;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivitySkuScopeQueryRepository {

    PageInfo<ActivityScopeConfigEntity> getPage(ActivityScopeConfigQueryParam param);

    ActivityScopeConfigEntity selectById(Long id);

    List<ActivityScopeConfigEntity> selectByCondition(ActivityScopeConfigQueryParam param);

    List<ActivityScopeConfigEntity> selectByInfoId(Long basicInfoId);
}
