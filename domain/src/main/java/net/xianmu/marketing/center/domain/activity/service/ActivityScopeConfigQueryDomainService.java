package net.xianmu.marketing.center.domain.activity.service;



import com.google.common.collect.Lists;
import net.xianmu.marketing.center.common.enums.activity.ScopeTypeEnum;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityScopeConfigQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuScopeQueryRepository;
import net.xianmu.marketing.center.domain.merchantpool.entity.MerchantPoolInfoEntity;
import net.xianmu.marketing.center.domain.merchantpool.param.query.MerchantPoolInfoQueryParam;
import net.xianmu.marketing.center.domain.merchantpool.repsitory.MerchantPoolInfoQueryRepository;
import net.xianmu.marketing.center.facade.area.AreaQueryFacade;
import net.xianmu.marketing.center.facade.area.dto.AreaSimpleDTO;
import net.xianmu.marketing.center.facade.wnc.WarehouseStorageFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 *
 * @Title: 活动生效范围配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivityScopeConfigQueryDomainService {

    @Autowired
    private ActivitySkuScopeQueryRepository activityScopeConfigQueryRepository;

    @Resource
    private MerchantPoolInfoQueryRepository merchantPoolInfoQueryRepository;

    @Resource
    private AreaQueryFacade areaQueryFacade;

    @Resource
    private WarehouseStorageFacade warehouseStorageFacade;


    public ActivityScopeConfigDTO buildScopeConfig(Long basicInfoId, Integer systemOrigin, boolean needDetail) {
        ActivityScopeConfigQueryParam queryParam = new ActivityScopeConfigQueryParam();
        queryParam.setBasicInfoId(basicInfoId);
        queryParam.setScopeTypes(ScopeTypeEnum.getCodeList(systemOrigin));
        List<ActivityScopeConfigEntity> activityScopeConfigEntities = activityScopeConfigQueryRepository.selectByCondition(queryParam);
        if (CollectionUtils.isEmpty(activityScopeConfigEntities)) {
            return null;
        }
        ActivityScopeConfigDTO scopeConfigDTO = new ActivityScopeConfigDTO();
        scopeConfigDTO.setScopeType(activityScopeConfigEntities.get(0).getScopeType());
        List<Long> scopeIds = activityScopeConfigEntities.stream().map(x -> x.getScopeId())
                .collect(Collectors.toList());
        scopeConfigDTO.setScopeIds(scopeIds);
        if (!needDetail) {
            return scopeConfigDTO;
        }
        List<ActivityScopeEntity> scopes = getScopes(scopeConfigDTO, scopeIds);
        scopeConfigDTO.setScopes(scopes);
        return scopeConfigDTO;
    }

    /**
     * @description 查询范围详情数据
     * @params [scopeConfigDTO, scopeIds]
     * @return java.util.List<net.xianmu.marketing.center.domain.activity.entity.ActivityScopeEntity>
     * <AUTHOR>
     * @date  2025/6/11 11:16
     */
    private List<ActivityScopeEntity> getScopes(ActivityScopeConfigDTO scopeConfigDTO, List<Long> scopeIds) {
        List<ActivityScopeEntity> scopes = Lists.newArrayList();
        switch (ScopeTypeEnum.getByCode(scopeConfigDTO.getScopeType())) {
            case ALL:
                break;
            case MERCHANT_POOL:
                MerchantPoolInfoQueryParam queryParam = new MerchantPoolInfoQueryParam();
                queryParam.setScopeIds(scopeIds);
                List<MerchantPoolInfoEntity> poolInfos = merchantPoolInfoQueryRepository.selectByCondition(queryParam);
                scopes = poolInfos.stream().map(t -> {
                    ActivityScopeEntity scopeDTO = new ActivityScopeEntity();
                    scopeDTO.setScopeId(t.getId());
                    scopeDTO.setScopeName(t.getName());
                    return scopeDTO;
                }).collect(Collectors.toList());
                break;
            case AREA:
                List<Integer> areaNos = scopeIds.stream()
                        .map(t -> Integer.valueOf(String.valueOf(t))).collect(Collectors.toList());
                List<AreaSimpleDTO> areaSimpleDTOS = areaQueryFacade.batchQueryByAreaNos(areaNos);
                scopes = areaSimpleDTOS.stream().map(t -> {
                    ActivityScopeEntity scopeDTO = new ActivityScopeEntity();
                    scopeDTO.setScopeId(t.getAreaNo().longValue());
                    scopeDTO.setScopeName(t.getAreaName());
                    return scopeDTO;
                }).collect(Collectors.toList());
                break;
            case LARGE_AREA:
                List<Integer> largeAreaNos = scopeIds.stream()
                        .map(t -> Integer.valueOf(String.valueOf(t))).collect(Collectors.toList());
                List<AreaSimpleDTO> largeAreas = areaQueryFacade.batchQueryByLargeAreaNos(largeAreaNos);
                scopes = largeAreas.stream().map(t -> {
                    ActivityScopeEntity scopeDTO = new ActivityScopeEntity();
                    scopeDTO.setScopeId(t.getLargeAreaNo().longValue());
                    scopeDTO.setScopeName(t.getLargeAreaName());
                    return scopeDTO;
                }).collect(Collectors.toList());
                break;
            case WAREHOUSE:
                List<Integer> warehouseNos = scopeIds.stream()
                        .map(t -> Integer.valueOf(String.valueOf(t))).collect(Collectors.toList());
                Map<Integer, String> warehouseBaseInfoByNo = warehouseStorageFacade.queryWarehouseBaseInfoByNo(warehouseNos);
                scopes = warehouseNos.stream().map(t -> {
                    ActivityScopeEntity scopeDTO = new ActivityScopeEntity();
                    scopeDTO.setScopeId(t.longValue());
                    scopeDTO.setScopeName(warehouseBaseInfoByNo.getOrDefault(t, null));
                    return scopeDTO;
                }).collect(Collectors.toList());
                break;
        }
        return scopes;
    }
}
