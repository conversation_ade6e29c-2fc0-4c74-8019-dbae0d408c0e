package net.xianmu.marketing.center.domain.activity.service;


import net.xianmu.marketing.center.domain.activity.repository.ActivityBasicInfoQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivityBasicInfoCommandRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityBasicInfoCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 活动基本信息表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivityBasicInfoCommandDomainService {


    @Autowired
    private ActivityBasicInfoCommandRepository activityBasicInfoCommandRepository;
    @Autowired
    private ActivityBasicInfoQueryRepository activityBasicInfoQueryRepository;



    public ActivityBasicInfoEntity insert(ActivityBasicInfoCommandParam param) {
        return activityBasicInfoCommandRepository.insertSelective(param);
    }


    public int update(ActivityBasicInfoCommandParam param) {
        return activityBasicInfoCommandRepository.updateSelectiveById(param);
    }
}
