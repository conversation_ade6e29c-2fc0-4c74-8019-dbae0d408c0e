package net.xianmu.marketing.center.domain.activity.param.query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Data
public class ActivityItemConfigQueryParam extends BasePageInput {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 基本信息id
	 */
	private Long basicInfoId;

	/**
	 * 商品选择方式，0 sku，1 类目，2 全部上架sku，3 标签商品，4 其他
	 */
	private Integer goodSelectWay;

	/**
	 * 商品定价类型，0 单品定价，1 满金额阶梯，2 满金额重复，3 满数量重复，4 满数量阶梯，5 其他
	 */
	private Integer pricingType;

	/**
	 * 商品定价类型扩展，目前主要是满减、满返的具体规则
	 */
	private String pricingTypeExt;

	/**
	 * 每件商品优惠幅度等于毛利的百分之X
	 */
	private Integer discountPercentage;

	/**
	 * 每件商品最多优惠X元
	 */
	private BigDecimal discount;

	/**
	 * 最后一次修改人id
	 */
	private Integer updaterId;

	/**
	 * 是否已被删除，0 否，1 是
	 */
	private Integer delFlag;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	

	
}