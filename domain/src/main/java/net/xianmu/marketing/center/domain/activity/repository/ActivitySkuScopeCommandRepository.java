package net.xianmu.marketing.center.domain.activity.repository;

import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityScopeConfigCommandParam;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivitySkuScopeCommandRepository {

    ActivityScopeConfigEntity insertSelective(ActivityScopeConfigCommandParam param);

    int updateSelectiveById(ActivityScopeConfigCommandParam param);

    List<ActivityScopeConfigEntity> insertBatch(List<ActivityScopeConfigCommandParam> scopeConfigParams);

    void updateDelFlag(Long basicInfoId, Long updaterId);
}
