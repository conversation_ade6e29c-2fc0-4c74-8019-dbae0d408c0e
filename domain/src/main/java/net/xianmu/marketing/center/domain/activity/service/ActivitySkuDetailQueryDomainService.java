package net.xianmu.marketing.center.domain.activity.service;


import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailCommandRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 活动sku配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivitySkuDetailQueryDomainService {


}
