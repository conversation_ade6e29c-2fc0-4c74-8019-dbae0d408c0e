package net.xianmu.marketing.center.domain.activity.converter;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuDetailCommandParam;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/18 13:57
 * @PackageName:net.xianmu.marketing.center.domain.activity.converter
 * @ClassName: ActivitySkuDetailConverter
 * @Description: TODO
 * @Version 1.0
 */
public class ActivitySkuDetailConverter {
    public static List<ActivitySkuDetailEntity> toActivitySkuDetailEntitys(List<ActivitySkuDetailEntity> skuDetailParams) {
        if (CollectionUtils.isEmpty(skuDetailParams)) {
            return null;
        }
        List<ActivitySkuDetailEntity> activitySkuDetailEntities = new ArrayList<>();
        skuDetailParams.forEach(param -> {
            ActivitySkuDetailEntity skuDetail = new ActivitySkuDetailEntity();
            skuDetail.setId(param.getId());
            skuDetail.setItemConfigId(param.getItemConfigId());
            skuDetail.setSku(param.getSku());
            skuDetail.setRoundingMode(param.getRoundingMode());
            skuDetail.setAdjustType(param.getAdjustType());
            skuDetail.setAmount(param.getAmount());
            skuDetail.setSort(param.getSort());
            skuDetail.setPlanQuantity(param.getPlanQuantity());
            skuDetail.setActualQuantity(param.getActualQuantity());
            skuDetail.setLockQuantity(param.getLockQuantity());
            skuDetail.setAccountLimit(param.getAccountLimit());
            skuDetail.setLimitQuantity(param.getLimitQuantity());
            skuDetail.setMinSaleNum(param.getMinSaleNum());
            skuDetail.setSingleDeposit(param.getSingleDeposit());
            skuDetail.setExpansionRatio(param.getExpansionRatio());
            skuDetail.setHidePrice(param.getHidePrice());
            skuDetail.setTimingConfig(param.getTimingConfig());
            skuDetail.setDelFlag(param.getDelFlag());
            skuDetail.setCreateTime(param.getCreateTime());
            skuDetail.setUpdateTime(param.getUpdateTime());
            skuDetail.setIsSupportTiming(param.getIsSupportTiming());
            skuDetail.setAutoPrice(param.getAutoPrice());
            skuDetail.setDiscountLabel(param.getDiscountLabel());
            skuDetail.setLadderConfig(param.getLadderConfig());
            skuDetail.setMarketingCostSharingRatio(param.getMarketingCostSharingRatio());
            activitySkuDetailEntities.add(skuDetail);
        });
        return activitySkuDetailEntities;
    }
}
