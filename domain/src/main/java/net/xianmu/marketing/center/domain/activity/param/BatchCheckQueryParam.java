package net.xianmu.marketing.center.domain.activity.param;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Data
public class BatchCheckQueryParam implements Serializable {

    /**
     * 活动基础信息id
     */
    private Long id;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * sku集合
     */
    private List<String> skus;

    /**
     * 活动范围
     */
    private List<ScopeQueryParam> scopeList;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否永久，0 否，1 是
     */
    private Integer isPermanent;

    /**
     * 归属人  平台默认为null 供应商supplier_id
     */
    private Long ownerId;

    /**
     * 创建人系统来源  0-srm, 2-admin
     */
    private Integer systemOrigin;

}
