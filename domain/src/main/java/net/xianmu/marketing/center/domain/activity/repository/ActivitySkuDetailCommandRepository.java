package net.xianmu.marketing.center.domain.activity.repository;

import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuDetailCommandParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-06-10 15:41:35
* @version 1.0
*
*/
public interface ActivitySkuDetailCommandRepository {

    ActivitySkuDetailEntity insertSelective(ActivitySkuDetailCommandParam param);

    int updateSelectiveById(ActivitySkuDetailCommandParam param);

    List<ActivitySkuDetailEntity> insertBatch(List<ActivitySkuDetailCommandParam> skuDetailParams);

    int updateDelFlag(Long itemConfigId, String sku);
}