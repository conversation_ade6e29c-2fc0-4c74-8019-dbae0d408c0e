package net.xianmu.marketing.center.domain.merchantpool.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-12-16 15:42:54
 * @version 1.0
 *
 */
@Data
public class MerchantPoolInfoQueryParam extends BasePageInput {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 分群名称
	 */
	private String name;

	/**
	 * 创建方式,0 在线圈选,1 Excel导入
	 */
	private Integer createWay;

	/**
	 * 更新方式,0 不支持,1 自动,2 手动
	 */
	private Integer updateWay;

	/**
	 * 数据状态
	 */
	private Integer status;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 当前最新版本号
	 */
	private Integer version;

	/**
	 * 创建者
	 */
	private String creator;

	/**
	 * 最后一次操作者
	 */
	private String updater;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 数据源 0-mysql计算 1-数仓计算
	 */
	private Integer dataSource;

	private List<Long> scopeIds;
}