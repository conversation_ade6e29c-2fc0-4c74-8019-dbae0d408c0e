package net.xianmu.marketing.center.domain.activity.service;


import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailCommandRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuDetailCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 *
 * @Title: 活动sku配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivitySkuDetailCommandDomainService {


    @Autowired
    private ActivitySkuDetailCommandRepository activitySkuDetailCommandRepository;
    @Autowired
    private ActivitySkuDetailQueryRepository activitySkuDetailQueryRepository;



    public ActivitySkuDetailEntity insert(ActivitySkuDetailCommandParam param) {
        return activitySkuDetailCommandRepository.insertSelective(param);
    }


    public int update(ActivitySkuDetailCommandParam param) {
        return activitySkuDetailCommandRepository.updateSelectiveById(param);
    }

    public List<ActivitySkuDetailEntity> insertBatch(List<ActivitySkuDetailCommandParam> skuDetailParams) {
        return activitySkuDetailCommandRepository.insertBatch(skuDetailParams);
    }

    public int updateDelFlag(Long itemConfigId, String sku) {
        return activitySkuDetailCommandRepository.updateDelFlag(itemConfigId, sku);
    }
}
