package net.xianmu.marketing.center.domain.activity.repository;

import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityBasicInfoCommandParam;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivityBasicInfoCommandRepository {

    ActivityBasicInfoEntity insertSelective(ActivityBasicInfoCommandParam param);

    int updateSelectiveById(ActivityBasicInfoCommandParam param);
}
