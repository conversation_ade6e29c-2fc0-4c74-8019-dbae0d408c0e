package net.xianmu.marketing.center.domain.activity.param.command;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Data
public class ActivitySkuPriceCommandParam {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * sku配置明细id
	 */
	private Long skuDetailId;

	/**
	 * 活动sku
	 */
	private String sku;

	/**
	 * 运营城市
	 */
	private Integer areaNo;

	/**
	 * 原价
	 */
	private BigDecimal salePrice;

	/**
	 * 阶梯价
	 */
	private String ladderPrice;

	/**
	 * 活动价
	 */
	private BigDecimal activityPrice;

	/**
	 * 最后一次修改人id
	 */
	private Integer updaterId;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 基本信息id
	 */
	private Long basicInfoId;

	

	
}