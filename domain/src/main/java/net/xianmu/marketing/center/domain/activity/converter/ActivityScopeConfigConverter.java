package net.xianmu.marketing.center.domain.activity.converter;

import net.xianmu.marketing.center.common.enums.activity.ScopeTypeEnum;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityScopeConfigCommandParam;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/18 13:51
 * @PackageName:net.xianmu.marketing.center.domain.activity.converter
 * @ClassName: ActivityScopeConfigConverter
 * @Description: TODO
 * @Version 1.0
 */
public class ActivityScopeConfigConverter {
    public static ActivityScopeConfigDTO toActivityScopeConfigDTO(List<ActivityScopeConfigEntity> scopeConfigParams) {
        if (CollectionUtils.isEmpty(scopeConfigParams)) {
            return null;
        }

        ActivityScopeConfigDTO activityScopeConfigDTO = new ActivityScopeConfigDTO();

        //过滤掉库存仓维度的配置
        scopeConfigParams = scopeConfigParams.stream().filter(x -> !Objects.equals(x.getScopeType(), ScopeTypeEnum.WAREHOUSE.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scopeConfigParams)) {
            return null;
        }
        activityScopeConfigDTO.setScopeType(scopeConfigParams.get(0).getScopeType());
        List<Long> scopeIds = scopeConfigParams.stream().map(x -> x.getScopeId()).collect(Collectors.toList());
        activityScopeConfigDTO.setScopeIds(scopeIds);
        return activityScopeConfigDTO;
    }
}
