package net.xianmu.marketing.center.domain.activity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/18 11:17
 * @PackageName:net.xianmu.marketing.center.application.inbound.controller.activity.dto
 * @ClassName: ActivitySkuDetailDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ActivitySkuDetailDTO {

    private Long id;

    /**
     * 商品配置id
     */
    private Long itemConfigId;

    /**
     * 活动sku
     */
    private String sku;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整
     */
    private Integer roundingMode;


    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    private BigDecimal amount;

    /**
     * 展示排序，数字越小，排序值最高
     */
    private Integer sort;

    /**
     * 计划数量，首次添加的活动库存
     */
    private Integer planQuantity;

    /**
     * 实际数量，最终添加的活动库存
     */
    private Integer actualQuantity;

    /**
     * 冻结数量，已使用的活动库存
     */
    private Integer lockQuantity;

    /**
     * 账号限购类型，0 不限购，1 件数，2 件/日
     */
    private Integer accountLimit;

    /**
     * 限购数量，0表示不限制
     */
    private Integer limitQuantity;

    /**
     * 起购数量，0表示不限制
     */
    private Integer minSaleNum;

    /**
     * 每件订金
     */
    private BigDecimal singleDeposit;

    /**
     * 膨胀倍数
     */
    private BigDecimal expansionRatio;

    /**
     * 是否隐价，0 否，1 是
     */
    private Integer hidePrice;

    /**
     * 省心送配置json
     */
    private String timingConfig;

    /**
     * 是否支持省心送特价，0 否，1 是
     */
    private Integer isSupportTiming;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 是否开启自动定价  1：开启  0：关闭
     */
    private Integer autoPrice;

    /**
     * 折扣率标签  0：不展示  1：展示  默认为不展示
     */
    private Integer discountLabel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 阶梯配置
     */
    private String ladderConfig;
}
