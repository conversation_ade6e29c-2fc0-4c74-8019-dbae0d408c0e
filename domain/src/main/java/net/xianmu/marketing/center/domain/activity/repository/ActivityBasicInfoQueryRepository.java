package net.xianmu.marketing.center.domain.activity.repository;

import java.util.List;

import com.github.pagehelper.PageInfo;
import net.xianmu.marketing.center.domain.activity.dto.ActivityPageRespDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemScopeEntity;
import net.xianmu.marketing.center.domain.activity.param.BatchCheckQueryParam;
import net.xianmu.marketing.center.domain.activity.param.ScopeQueryParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicPageQueryParam;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivityBasicInfoQueryRepository {

    List<ActivityItemScopeEntity> listByScope(List<ScopeQueryParam> list, Integer type, Integer activityStatus);

    List<ActivityItemScopeEntity> selectByScope(BatchCheckQueryParam queryParam);

    PageInfo<ActivityBasicInfoEntity> getPage(ActivityBasicInfoQueryParam param);

    ActivityBasicInfoEntity selectById(Long id);

    List<ActivityBasicInfoEntity> selectByCondition(ActivityBasicInfoQueryParam param);

    List<ActivityPageRespDTO> listByQuery(ActivityBasicPageQueryParam param);

}
