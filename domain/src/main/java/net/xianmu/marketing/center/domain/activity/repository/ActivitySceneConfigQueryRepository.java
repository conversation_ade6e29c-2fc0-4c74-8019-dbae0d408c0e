package net.xianmu.marketing.center.domain.activity.repository;




import com.github.pagehelper.PageInfo;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySceneConfigQueryParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-06-10 15:41:35
* @version 1.0
*
*/
public interface ActivitySceneConfigQueryRepository {

    PageInfo<ActivitySceneConfigEntity> getPage(ActivitySceneConfigQueryParam param);

    ActivitySceneConfigEntity selectById(Long id);

    List<ActivitySceneConfigEntity> selectByCondition(ActivitySceneConfigQueryParam param);

    ActivitySceneConfigEntity selectByBasicInfoId(Long basicInfoId);
}