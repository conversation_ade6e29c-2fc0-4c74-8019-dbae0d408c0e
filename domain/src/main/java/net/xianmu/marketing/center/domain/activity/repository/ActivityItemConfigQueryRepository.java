package net.xianmu.marketing.center.domain.activity.repository;

import com.github.pagehelper.PageInfo;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityItemConfigQueryParam;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/27
 */
public interface ActivityItemConfigQueryRepository {

    ActivityItemConfigEntity selectByInfoId(Long basicInfoId);

    PageInfo<ActivityItemConfigEntity> getPage(ActivityItemConfigQueryParam param);

    ActivityItemConfigEntity selectById(Long id);

    List<ActivityItemConfigEntity> selectByCondition(ActivityItemConfigQueryParam param);
}
