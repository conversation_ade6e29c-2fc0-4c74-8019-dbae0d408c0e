package net.xianmu.marketing.center.domain.activity.param.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeQueryDTO;

/**
 * @author: <EMAIL>
 * @create: 2022/12/6
 */
@Data
public class ActivityBasicPageQueryParam implements Serializable {

    /**
     * 活动id(查询需要排除当前活动)
     */
    private Long id;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 是否永久，0 否，1 是
     */
    private Integer isPermanent;

    /**
     * 活动范围类型，0 全部，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;

    /**
     * 范围id，全部的时候值为0
     */
    private List<Long> scopeIds;

    private Integer creatorId;

    /**
     * 活动状态，0 未生效，1 已生效，2 已失效
     */
    private Integer activityStatus;

    /**
     * 活动范围，单个查询使用
     */
    private Long scopeId;

    /**
     * 活动名称
     */
    private String name;

    private String sku;

    /**
     * 活动范围集合
     */
    private List<ActivityScopeQueryDTO> scopeList;


}
