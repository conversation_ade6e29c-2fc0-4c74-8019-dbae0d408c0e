package net.xianmu.marketing.center.domain.activity.repository;

import java.util.ArrayList;
import java.util.List;

import com.github.pagehelper.PageInfo;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuDetailQueryParam;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivitySkuDetailQueryRepository {

    List<ActivitySkuDetailEntity> listByItemConfigsSkus(List<Long> itemConfigIds, List<String> skus);

    int countByItemConfig(Long itemConfigId);

    PageInfo<ActivitySkuDetailEntity> getPage(ActivitySkuDetailQueryParam param);

    ActivitySkuDetailEntity selectById(Long id);

    List<ActivitySkuDetailEntity> selectByCondition(ActivitySkuDetailQueryParam param);

    ActivitySkuDetailEntity selectByItemConfigIdAndSku(Long itemConfigId, String sku);

    List<ActivitySkuDetailEntity> listByBasicInfoIds(ArrayList<Long> newArrayList, String sku);

    List<ActivitySkuDetailEntity> listBySkusAndOwnerId(ActivitySkuDetailQueryParam param);
}
