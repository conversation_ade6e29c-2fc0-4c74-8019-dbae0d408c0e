package net.xianmu.marketing.center.domain.activity.param.query;

import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Data
public class ActivitySceneConfigQueryParam extends BasePageInput {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 基本信息id
	 */
	private Long basicInfoId;

	/**
	 * 生效平台，0 商城，1 直播，2 其他
	 */
	private Integer platform;

	/**
	 * 投放区域
	 */
	private Integer place;

	/**
	 * 是否已被删除，0 否，1 是
	 */
	private Integer delFlag;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	

	
}