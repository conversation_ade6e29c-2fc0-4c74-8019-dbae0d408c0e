package net.xianmu.marketing.center.domain.activity.repository;

import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityItemConfigCommandParam;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/27
 */
public interface ActivityItemConfigCommandRepository {

    int updateBatchByItemConfigId(Long itemConfigId, List<String> skus);

    ActivityItemConfigEntity insertSelective(ActivityItemConfigCommandParam param);

    int updateSelectiveById(ActivityItemConfigCommandParam param);

    Long updateDelFlag(ActivityItemConfigCommandParam itemConfig);
}
