package net.xianmu.marketing.center.domain.activity.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Data
public class ActivityScopeConfigQueryParam extends BasePageInput {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 基础信息id
	 */
	private Long basicInfoId;

	/**
	 * 范围id （人群包：merchant_pool_info主键ID，运营城市：areaNo，运营大区：largeAreaNo）
	 */
	private Long scopeId;

	/**
	 * 活动范围类型，1 人群包，2 运营城市，3 运营大区  4 库存仓
	 */
	private Integer scopeType;

	/**
	 * 最后一次修改人id
	 */
	private Integer updaterId;

	/**
	 * 是否已被删除，0 否，1 是
	 */
	private Integer delFlag;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 活动范围类型，1 人群包，2 运营城市，3 运营大区 4 库存仓
	 */
	private List<Integer> scopeTypes;

	
}