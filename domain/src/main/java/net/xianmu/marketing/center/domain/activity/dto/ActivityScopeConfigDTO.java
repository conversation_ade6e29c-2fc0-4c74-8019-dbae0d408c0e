package net.xianmu.marketing.center.domain.activity.dto;

import lombok.Data;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17 16:38
 * @PackageName:net.xianmu.marketing.center.domain.activity.dto
 * @ClassName: ActivityScopeConfigDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ActivityScopeConfigDTO {

    /**
     * 活动范围类型，0 全部，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;

    private List<Long> scopeIds;

    private List<ActivityScopeEntity> scopes;
}
