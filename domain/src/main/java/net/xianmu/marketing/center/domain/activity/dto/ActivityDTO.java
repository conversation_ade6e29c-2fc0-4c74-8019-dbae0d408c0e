package net.xianmu.marketing.center.domain.activity.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/18 11:22
 * @PackageName:net.xianmu.marketing.center.application.inbound.controller.activity.dto
 * @ClassName: ActivityDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ActivityDTO {

    /**
     * 活动基础信息
     */
    private ActivityBasicInfoDTO basicInfoDTO;

    /**
     * 活动范围配置信息
     */
    private ActivityScopeConfigDTO scopeConfigDTO;

    /**
     * 商品配置项
     */
    private ActivityItemConfigDTO itemConfigDTO;

    /**
     * 操作人ID
     */
    private Integer adminId;
}
