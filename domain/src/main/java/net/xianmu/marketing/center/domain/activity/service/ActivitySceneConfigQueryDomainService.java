package net.xianmu.marketing.center.domain.activity.service;


import net.xianmu.marketing.center.domain.activity.repository.ActivitySceneConfigQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySceneConfigCommandRepository;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySceneConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 活动场景配置表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
@Service
public class ActivitySceneConfigQueryDomainService {


}
