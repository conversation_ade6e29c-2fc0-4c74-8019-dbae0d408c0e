package net.xianmu.marketing.center.domain.activity.converter;

import net.xianmu.marketing.center.domain.activity.dto.ActivityItemConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;

/**
 * <AUTHOR>
 * @Date 2025/6/17 16:54
 * @PackageName:net.xianmu.marketing.center.domain.activity.converter
 * @ClassName: ActivityItemConfigConverter
 * @Description: TODO
 * @Version 1.0
 */
public class ActivityItemConfigConverter {
    public static ActivityItemConfigDTO toActivityItemConfigDTO(ActivityItemConfigEntity itemConfig) {
        if (itemConfig == null) {
            return null;
        }
        ActivityItemConfigDTO activityItemConfigDTO = new ActivityItemConfigDTO();
        activityItemConfigDTO.setId(itemConfig.getId());
        activityItemConfigDTO.setBasicInfoId(itemConfig.getBasicInfoId());
        activityItemConfigDTO.setGoodSelectWay(itemConfig.getGoodSelectWay());
        activityItemConfigDTO.setPricingType(itemConfig.getPricingType());
        activityItemConfigDTO.setPricingTypeExt(itemConfig.getPricingTypeExt());
        activityItemConfigDTO.setDiscountPercentage(itemConfig.getDiscountPercentage());
        activityItemConfigDTO.setDiscount(itemConfig.getDiscount());
        activityItemConfigDTO.setUpdaterId(itemConfig.getUpdaterId());
        activityItemConfigDTO.setDelFlag(itemConfig.getDelFlag());
        activityItemConfigDTO.setCreateTime(itemConfig.getCreateTime());
        activityItemConfigDTO.setUpdateTime(itemConfig.getUpdateTime());
        return activityItemConfigDTO;
    }
}
