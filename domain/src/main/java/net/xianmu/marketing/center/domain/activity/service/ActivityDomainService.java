package net.xianmu.marketing.center.domain.activity.service;

import static java.util.stream.Collectors.toList;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.marketing.center.common.config.NacosPropertiesHolder;
import net.xianmu.marketing.center.common.constant.Conf;
import net.xianmu.marketing.center.common.converter.RocketMqConstant;
import net.xianmu.marketing.center.common.enums.activity.ActivityTypeEnum;
import net.xianmu.marketing.center.common.enums.activity.GoodSelectWayEnum;
import net.xianmu.marketing.center.common.enums.activity.ScopeTypeEnum;
import net.xianmu.marketing.center.common.enums.feishu.ProcessInstanceBizTypeEnum;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.common.enums.mq.MType;
import net.xianmu.marketing.center.common.input.DingdingFormBO;
import net.xianmu.marketing.center.common.input.MQData;
import net.xianmu.marketing.center.common.input.ProcessInstanceCreateBO;
import net.xianmu.marketing.center.domain.activity.converter.ActivityBasicInfoConverter;
import net.xianmu.marketing.center.domain.activity.converter.ActivityItemConfigConverter;
import net.xianmu.marketing.center.domain.activity.converter.ActivityScopeConfigConverter;
import net.xianmu.marketing.center.domain.activity.converter.ActivitySkuDetailConverter;
import net.xianmu.marketing.center.domain.activity.dto.ActivityBasicInfoDTO;
import net.xianmu.marketing.center.domain.activity.dto.ActivityDTO;
import net.xianmu.marketing.center.domain.activity.dto.ActivityItemConfigDTO;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.*;
import net.xianmu.marketing.center.domain.activity.param.BatchCheckQueryParam;
import net.xianmu.marketing.center.domain.activity.param.BatchDeleteQueryParam;
import net.xianmu.marketing.center.domain.activity.param.ScopeQueryParam;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityScopeConfigCommandParam;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuDetailCommandParam;
import net.xianmu.marketing.center.domain.activity.repository.*;
import net.xianmu.marketing.center.facade.area.AreaQueryFacade;
import net.xianmu.marketing.center.facade.area.dto.AreaSimpleDTO;
import net.xianmu.marketing.center.facade.item.ItemQueryFacade;
import net.xianmu.marketing.center.facade.item.dto.SkuInfoDetailDTO;
import net.xianmu.marketing.center.facade.item.input.BatchSkuInput;
import net.xianmu.marketing.center.facade.wnc.WarehouseStorageFacade;
import net.xianmu.marketing.center.facade.wnc.dto.WarehouseBaseInfoByNoDTO;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Service
public class ActivityDomainService {

    @Resource
    private ActivityBasicInfoQueryRepository basicInfoQueryRepository;

    @Resource
    private ActivitySkuDetailQueryRepository skuDetailQueryRepository;

    @Resource
    private ActivitySkuPriceCommandRepository skuPriceCommandRepository;

    @Resource
    private ActivityItemConfigQueryRepository itemConfigQueryRepository;

    @Resource
    private ActivityItemConfigCommandRepository itemConfigCommandRepository;

    @Resource
    private AreaQueryFacade areaQueryFacade;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private ItemQueryFacade itemQueryFacade;

    @Resource
    private WarehouseStorageFacade warehouseStorageFacade;

    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;

    public List<RepeatActivitySkuEntity> listRepeatActivity(BatchCheckQueryParam param) {
        List<RepeatActivitySkuEntity> list = Lists.newArrayList();
        List<ScopeQueryParam> scopeList = param.getScopeList();
        if (Objects.equals(param.getIsPermanent(), 1)) {
            param.setStartTime(LocalDateTime.now());
            param.setEndTime(LocalDateTime.of(2099, 12, 31, 23, 59, 59));
        }
        //判断活动范围类型，运营区域和运营大区都需
        Integer scopeType = scopeList.get(0).getScopeType();
        //运营城市的转换成运营大区
        if (Objects.equals(ScopeTypeEnum.AREA.getCode(), scopeType)) {
            List<Integer> areaNos = scopeList.stream().map(x -> x.getScopeId().intValue()).distinct().collect(toList());
            List<AreaSimpleDTO> areaSimpleList = areaQueryFacade.batchQueryByAreaNos(areaNos);
            List<ScopeQueryParam> queryParams = areaSimpleList.stream()
                    .filter(x -> Objects.equals(x.getAreaStatus(), 1) && Objects.equals(x.getLargeAreaStatus(), 1))
                    .map(t -> new ScopeQueryParam(Long.valueOf(t.getLargeAreaNo()), ScopeTypeEnum.LARGE_AREA.getCode())).distinct()
                    .collect(toList());
            scopeList.addAll(queryParams);
        }
        //运营大区的转换成运营城市
        if (Objects.equals(ScopeTypeEnum.LARGE_AREA.getCode(), scopeType)) {
            List<Integer> largeAreaNos = scopeList.stream().map(x -> x.getScopeId().intValue()).distinct().collect(toList());
            List<AreaSimpleDTO> areaSimpleList = areaQueryFacade.batchQueryByLargeAreaNos(largeAreaNos);
            List<ScopeQueryParam> queryParams = areaSimpleList.stream()
                    .filter(x -> Objects.equals(x.getAreaStatus(), 1) && Objects.equals(x.getLargeAreaStatus(), 1))
                    .map(t -> new ScopeQueryParam(Long.valueOf(t.getAreaNo()), ScopeTypeEnum.AREA.getCode())).distinct()
                    .collect(toList());
            scopeList.addAll(queryParams);
        }

        if (Objects.equals(ScopeTypeEnum.MERCHANT_POOL.getCode(), scopeType)) {
            //不需要转换
        }

        //根据活动范围获取在开始时间&&结束时间有重叠的活动
        List<ActivityItemScopeEntity> validActivityList = basicInfoQueryRepository.selectByScope(param);
        //validActivityList有可能是configId不一样，所以转map需要处理下，防止重复
        Map<Long, ActivityItemScopeEntity> itemScopeEntityMap = validActivityList.stream()
                .collect(Collectors.toMap(ActivityItemScopeEntity::getItemConfigId, Function.identity(), (x, y) -> x));
        if (CollectionUtil.isEmpty(validActivityList)) {
            log.info("【活动商品校验】当前生效时间区间活动范围内没有重复的特价活动");
            return list;
        }

        List<Long> itemConfigIds = Lists.newArrayList(itemScopeEntityMap.keySet());
        List<String> skus = param.getSkus();
        //获取存在活动中的重复sku
        List<ActivitySkuDetailEntity> skuDetailList = skuDetailQueryRepository.listByItemConfigsSkus(itemConfigIds, skus);

        if (CollectionUtil.isEmpty(skuDetailList)) {
            log.info("【活动商品校验】当前生效时间区间活动范围内商品没有重复存在于其他特价活动中");
            return list;
        }

        //根据sku过滤有效的活动
        Map<String, List<ActivityItemScopeEntity>> listMap = skuDetailList.stream()
                .collect(Collectors.groupingBy(ActivitySkuDetailEntity::getSku, Collectors.mapping(x -> {
                    ActivityItemScopeEntity itemScopeEntity = itemScopeEntityMap.get(x.getItemConfigId());
                    return itemScopeEntity;
                }, toList())));
        listMap.forEach((k, v) -> {
                    RepeatActivitySkuEntity repeatActivitySku = new RepeatActivitySkuEntity();
                    repeatActivitySku.setSku(k);
                    repeatActivitySku.setSimpleInfoList(v);
                    list.add(repeatActivitySku);
                }
        );

        return list;
    }

    public Boolean batchDeleteItem(BatchDeleteQueryParam param) {
        //校验活动是否存在
        Long basicInfoId = param.getId();
        ActivityBasicInfoEntity basicInfo = basicInfoQueryRepository.selectById(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        //临保活动不支持创建、修改
        if (Objects.equals(basicInfo.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }
        List<String> skus = param.getSkus();
        ActivityItemConfigEntity itemConfig = itemConfigQueryRepository.selectByInfoId(basicInfoId);
        Long itemConfigId = itemConfig.getId();
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), itemConfig.getGoodSelectWay())) {
            int total = skuDetailQueryRepository.countByItemConfig(itemConfigId);
            if (total <= 1 || total - skus.size() < 1) {
                throw new BizException("不支持删除，活动sku至少保留一条");
            }
            itemConfigCommandRepository.updateBatchByItemConfigId(itemConfigId, skus);
            //需要联动删除价格信息
            skuPriceCommandRepository.deleteBySkus(basicInfoId, skus);
        }
        return Boolean.TRUE;
    }

    @Async("asyncExecutor")
    public void syncSendFeiShuApproval(ActivityBasicInfoEntity basicInfoEntity, List<ActivityScopeConfigEntity> scopeConfigParams,
                                       List<ActivitySkuDetailEntity> skuDetailParams, ActivityItemConfigEntity itemConfigEntity, Boolean isSendFeiShuApproval) {
        //处理计算活动价&&倒挂监控处理
        ActivityDTO activityDTO = new ActivityDTO();
        ActivityBasicInfoDTO activityBasicInfoDTO = ActivityBasicInfoConverter.toActivityBasicInfoDTO(basicInfoEntity);
        activityDTO.setBasicInfoDTO(activityBasicInfoDTO);
        activityDTO.setAdminId(basicInfoEntity.getCreatorId());

        ActivityScopeConfigDTO scopeConfigDTO = ActivityScopeConfigConverter.toActivityScopeConfigDTO(scopeConfigParams);
        activityDTO.setScopeConfigDTO(scopeConfigDTO);
        ActivityItemConfigDTO activityItemConfigDTO = ActivityItemConfigConverter.toActivityItemConfigDTO(itemConfigEntity);
        List<ActivitySkuDetailEntity> skuDetailList = ActivitySkuDetailConverter.toActivitySkuDetailEntitys(skuDetailParams);
        activityItemConfigDTO.setSkuDetailList(skuDetailList);
        activityDTO.setItemConfigDTO(activityItemConfigDTO);
        mqProducer.send(RocketMqConstant.Topic.TOPIC_ACTIVITY_COMMON_LIS, RocketMqConstant.Tag.ACTIVITY_CALCULATION_PRICE, activityDTO);

        if (!isSendFeiShuApproval) {
            log.info("【FeiShu审批】当前活动，无需进行FeiShu审批");
            return;
        }

        //开始发送飞书审批流
        if (!Objects.equals(basicInfoEntity.getSystemOrigin(), SystemOriginEnum.SRM.getType())) {
            log.info("【FeiShu审批】当前活动非SRM系统发起，不进行FeiShu审批");
            return;
        }
        if (CollectionUtils.isEmpty(skuDetailParams)) {
            log.info("【FeiShu审批】当前活动无商品，不进行FeiShu审批");
            return;
        }

        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.SPECIAL_PRICE_APPLICATION_BY_SUPPLIER);

        // 发起人adminId
        processInstanceCreateBO.setAdminId(nacosPropertiesHolder.getDefaultFeiShuSubmitter());

        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(basicInfoEntity.getId());

        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(9);

        DingdingFormBO df1 = new DingdingFormBO();
        df1.setFormName("发起人");
        df1.setFormValue(basicInfoEntity.getOwnerName());
        dingForms.add(df1);

        DingdingFormBO df2 = new DingdingFormBO();
        df2.setFormName("活动名称");
        df2.setFormValue(basicInfoEntity.getName());
        dingForms.add(df2);

        DingdingFormBO df3 = new DingdingFormBO();
        df3.setFormName("生效周期");
        if (Objects.equals(basicInfoEntity.getIsPermanent(), CommonStatus.NO.getCode())) {
            df3.setFormValue(BaseDateUtils.localDateTimeToString(basicInfoEntity.getStartTime())
                    + "-" + BaseDateUtils.localDateTimeToString(basicInfoEntity.getEndTime()));
        } else {
            df3.setFormValue("长期有效");
        }
        dingForms.add(df3);

        DingdingFormBO df4 = new DingdingFormBO();
        df4.setFormName("活动sku详细信息");
        StringBuilder sb = new StringBuilder();
        List<String> skuList = skuDetailParams.stream().map(ActivitySkuDetailEntity::getSku).collect(toList());
        BatchSkuInput input = new BatchSkuInput();
        input.setSkus(skuList);
        List<SkuInfoDetailDTO> skuInfoDetailDTOS = itemQueryFacade.listBySkusNew(input);
        Map<String, SkuInfoDetailDTO> skuInfoDetailDTOMap = skuInfoDetailDTOS.stream().collect(Collectors.
                toMap(SkuInfoDetailDTO::getSku, Function.identity(), (v1, v2) -> v1));

        List<Integer> warehouseNos = scopeConfigParams.stream().filter(item -> Objects.equals(item.getScopeType(),
                ScopeTypeEnum.WAREHOUSE.getCode())).map(item -> item.getScopeId().intValue()).collect(toList());
        if (CollectionUtils.isEmpty(warehouseNos)) {
            log.info("【FeiShu审批】当前活动无库存仓，不进行FeiShu审批");
            return;
        }
        Map<Integer, String> warehouseBaseInfoByNo = warehouseStorageFacade.queryWarehouseBaseInfoByNo(warehouseNos);
        String warehouseName = warehouseBaseInfoByNo.getOrDefault(warehouseNos.get(0), null);

        for (int i = 0; i < skuDetailParams.size(); i++) {
            try {
                ActivitySkuDetailEntity param = skuDetailParams.get(i);
                SkuInfoDetailDTO skuInfoDetailDTO = skuInfoDetailDTOMap.get(param.getSku());
                if (skuInfoDetailDTO == null) {
                    log.info("【FeiShu审批】当前活动sku不存在，sku:" + skuDetailParams.get(i).getSku());
                    continue;
                }
                sb.append("SKU").append(i + 1).append(":").append(skuInfoDetailDTO.getPdName()).append("&").
                        append(skuInfoDetailDTO.getWeight()).append("&").append(skuInfoDetailDTO.getSku()).append("/n");
                sb.append("库存仓:").append(warehouseName).append("/n");
                sb.append("临保风险数量:").append("11").append("/n");
                sb.append("特价售价:").append(skuDetailParams).append("/n");
                sb.append("特价库存数量:").append(param.getActualQuantity()).append("/n");
                sb.append("营销费用承担方:").append("供应商").append("/n");
            } catch (Exception e) {
                log.warn("【FeiShu审批】当前活动sku添加失败，sku:{}, cause:{}" + skuDetailParams.get(i).getSku(), Throwables.getStackTraceAsString(e));
            }
        }
        df4.setFormValue(sb.toString());
        dingForms.add(df4);

        DingdingFormBO df5 = new DingdingFormBO();
        df5.setFormName("特价管理详情链接");
        df5.setFormValue(Conf.DOMAIN_NAME + Conf.ACTIVITY_DETAIL_URL + basicInfoEntity.getId());
        dingForms.add(df5);
        processInstanceCreateBO.setDingdingForms(dingForms);
        mqProducer.send(RocketMqConstant.Topic.TOPIC_ACTIVITY_COMMON_LIS, RocketMqConstant.Tag.SPECIAL_PRICE_APPLICATION_BY_SUPPLIER, processInstanceCreateBO);
    }
}
