package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2022/12/1
 */
@Data
public class ActivityScopeConfigInput implements Serializable {

    /**
     * 基础信息id
     */
    private Long basicInfoId;

    /**
     * 范围id集合，全部的时候值为0
     */
    @NotEmpty(message = "请配置活动范围")
    private List<Long> scopeIds;

    private List<ActivityScopeInput> scopes;

    /**
     * 活动范围类型，0 全部，1 人群包，2 运营城市，3 运营大区  5 库存仓
     * todo 废弃掉0 全部, 业务不好处理
     */
    @NotNull(message = "请选择活动范围类型")
    private Integer scopeType;

    private Long updaterId;
}
