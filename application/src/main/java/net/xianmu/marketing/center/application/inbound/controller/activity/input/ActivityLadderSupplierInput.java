package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/5/14 14:34
 */
@Data
public class ActivityLadderSupplierInput implements Serializable {

    /**
     * 阶梯数
     */
    private Integer unit;

    /**
     * 原价
     */
    private BigDecimal salePrice;

    /**
     * 结算价
     */
    private BigDecimal settlementPrice;

    /**
     * 特价
     */
    private BigDecimal activityPrice;
}
