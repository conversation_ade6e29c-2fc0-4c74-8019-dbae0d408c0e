package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/10 13:59
 * @PackageName:net.xianmu.marketing.center.application.inbound.controller.activity.input
 * @ClassName: ActivityBatchSkuInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ActivityBatchSkuInput implements Serializable {

    /**
     * 文件链接--Excel导入的文件链接（Excel方式为必填）
     */
    @NotNull(message = "文件链接不能为空")
    private String key;
}
