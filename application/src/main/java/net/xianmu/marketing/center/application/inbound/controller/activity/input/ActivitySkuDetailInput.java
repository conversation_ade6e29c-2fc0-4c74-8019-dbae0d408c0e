package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2022/12/1
 */
@Data
public class ActivitySkuDetailInput implements Serializable {

    private Long id;

    /**
     * 活动sku
     */
    @NotNull(message = "sku不能为空")
    private String sku;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整（定价方式）
     */
    //@NotNull(message = "请选择定价方式")
    private Integer roundingMode;

    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    //@NotNull(message = "请选择价格调整方式")
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    //@NotNull(message = "请填写调价幅度")
    private BigDecimal amount;

    /**
     * 展示排序，数字越小，排序值最高
     */
    private Integer sort;

    /**
     * 计划数量，首次添加的活动库存
     */
    private Integer planQuantity;

    /**
     * 实际数量，最终添加的活动库存
     */
    private Integer actualQuantity;

    /**
     * 冻结数量，已使用的活动库存
     */
    private Integer lockQuantity;

    /**
     * 账号限购类型，0 不限购，1 件数，2 件/日
     */
    @NotNull(message = "请选择限号限购方式")
    private Integer accountLimit;

    /**
     * 限购数量，0表示不限制
     */
    private Integer limitQuantity;

    /**
     * 起购数量，0表示不限制
     */
    private Integer minSaleNum;

    /**
     * 每件订金
     */
    private BigDecimal singleDeposit;

    /**
     * 膨胀倍数
     */
    private BigDecimal expansionRatio;

    /**
     * 是否隐价，0 否，1 是
     */
    private Integer hidePrice;

    /**
     * 省心送配置json
     */
    private String timingConfig;

    /**
     * 是否支持省心送特价，0 否，1 是
     */
    private Integer isSupportTiming;

    /**
     * 是否开启自动定价  1：开启    0：关闭
     */
    private Integer autoPrice;

    /**
     * 折扣率标签  0：不展示  1：展示  默认为不展示
     */
    private Integer discountLabel;

    /***************以下为sku信息部分******************/

    private String skuName;

    private String weight;

    private String unit;

    private String logo;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;


    /***************以下为阶梯配置信息部分******************/

    /**
     * 阶梯配置
     */
    @Valid
    private List<ActivityLadderConfigInput> activityLadderConfigDTOList;

    /**
     * 阶梯结算价配置--srm创建
     */
    private List<ActivityLadderSupplierInput> activityLadderSupplierInputs;

    /**
     * 营销费用承担比例 举例{"platformSharingRatio":"100%","supplierSharingRatio":"0%"}
     */
    private String marketingCostSharingRatio;
}
