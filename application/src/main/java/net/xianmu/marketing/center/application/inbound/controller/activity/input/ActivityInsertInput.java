package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/10 11:28
 * @PackageName:net.xianmu.marketing.center.application.inbound.controller.activity.input
 * @ClassName: ActivityInsertInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ActivityInsertInput implements Serializable {
    /**
     * 活动基础信息
     */
    @Valid
    @NotNull(message = "活动基础信息不能为空")
    private ActivityBasicInfoInput basicInfoDTO;

    /**
     * 商品配置信息
     */
    @Valid
    @NotNull(message = "商品配置信息不能为空")
    private ActivityItemConfigInput itemConfigDTO;

    /**
     * 活动范围配置信息
     */
    @Valid
    @NotNull(message = "活动范围配置信息不能为空")
    private ActivityScopeConfigInput scopeConfigDTO;

    /**
     * 操作人ID
     */
    private Long creatorId;
}
