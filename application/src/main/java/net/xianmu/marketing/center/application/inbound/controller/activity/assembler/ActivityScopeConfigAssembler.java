package net.xianmu.marketing.center.application.inbound.controller.activity.assembler;


import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.ActivityScopeConfigInput;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.ActivityScopeConfigVO;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.ActivityScopeVO;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityScopeConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityScopeConfigCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
public class ActivityScopeConfigAssembler {

    private ActivityScopeConfigAssembler() {
        // 无需实现
    }



// ------------------------------- response ----------------------------

    public static List<ActivityScopeConfigVO> toActivityScopeConfigVOList(List<ActivityScopeConfigEntity> activityScopeConfigEntityList) {
        if (activityScopeConfigEntityList == null) {
            return Collections.emptyList();
        }
        List<ActivityScopeConfigVO> activityScopeConfigVOList = new ArrayList<>();
        for (ActivityScopeConfigEntity activityScopeConfigEntity : activityScopeConfigEntityList) {
            activityScopeConfigVOList.add(toActivityScopeConfigVO(activityScopeConfigEntity));
        }
        return activityScopeConfigVOList;
}


   public static ActivityScopeConfigVO toActivityScopeConfigVO(ActivityScopeConfigEntity activityScopeConfigEntity) {
       if (activityScopeConfigEntity == null) {
            return null;
       }
       ActivityScopeConfigVO activityScopeConfigVO = new ActivityScopeConfigVO();
       activityScopeConfigVO.setId(activityScopeConfigEntity.getId());
       activityScopeConfigVO.setBasicInfoId(activityScopeConfigEntity.getBasicInfoId());
       activityScopeConfigVO.setScopeId(activityScopeConfigEntity.getScopeId());
       activityScopeConfigVO.setScopeType(activityScopeConfigEntity.getScopeType());
       activityScopeConfigVO.setUpdaterId(activityScopeConfigEntity.getUpdaterId());
       activityScopeConfigVO.setDelFlag(activityScopeConfigEntity.getDelFlag());
       activityScopeConfigVO.setCreateTime(activityScopeConfigEntity.getCreateTime());
       activityScopeConfigVO.setUpdateTime(activityScopeConfigEntity.getUpdateTime());
       return activityScopeConfigVO;
   }

    public static List<ActivityScopeConfigCommandParam> toActivityScopeConfigCommandParamList(ActivityScopeConfigInput scopeConfigDTO,
                                                                                              Long basicInfoId, Long creatorId) {
        Integer scopeType = scopeConfigDTO.getScopeType();
        List<ActivityScopeConfigCommandParam> scopeConfigs = scopeConfigDTO.getScopeIds()
                .stream().map(x -> {
                    ActivityScopeConfigCommandParam scopeConfig = new ActivityScopeConfigCommandParam();
                    scopeConfig.setBasicInfoId(basicInfoId);
                    scopeConfig.setScopeId(x);
                    scopeConfig.setScopeType(scopeType);
                    scopeConfig.setUpdaterId(creatorId.intValue());
                    scopeConfig.setDelFlag(CommonStatus.NO.getCode());
                    return scopeConfig;
                }).collect(Collectors.toList());
        return scopeConfigs;
    }

    public static ActivityScopeConfigVO toActivityScopeConfigVO2(ActivityScopeConfigDTO activityScopeConfig) {
        if (activityScopeConfig == null) {
            return null;
        }
        ActivityScopeConfigVO activityScopeConfigVO = new ActivityScopeConfigVO();

        //活动范围详情
        activityScopeConfigVO.setScopeIds(activityScopeConfig.getScopeIds());
        activityScopeConfigVO.setScopeType(activityScopeConfig.getScopeType());
        List<ActivityScopeVO> scopes = new ArrayList<>();
        activityScopeConfig.getScopes().forEach(x -> {
            ActivityScopeVO scopeVO = new ActivityScopeVO();
            scopeVO.setScopeId(x.getScopeId());
            scopeVO.setScopeName(x.getScopeName());
            scopes.add(scopeVO);
        });
        activityScopeConfigVO.setScopes(scopes);
        return activityScopeConfigVO;
    }

    public static List<ActivityScopeConfigCommandParam> toActivityScopeConfigCommandParam(ActivityScopeConfigDTO activityScopeConfig) {


        return null;
    }
}
