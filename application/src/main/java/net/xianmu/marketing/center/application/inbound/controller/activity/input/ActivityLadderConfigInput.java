package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/5/14 14:34
 */
@Data
public class ActivityLadderConfigInput implements Serializable {

    /**
     * 阶梯数
     */
    @NotNull(message = "请选择数量")
    private Integer unit;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整（定价方式）
     */
    @NotNull(message = "请选择定价方式")
    private Integer roundingMode;

    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    @NotNull(message = "请选择价格调整方式")
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    @NotNull(message = "请填写调价幅度")
    private BigDecimal amount;
}
