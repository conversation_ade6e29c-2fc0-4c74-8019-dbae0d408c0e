package net.xianmu.marketing.center.application.inbound.controller.tabsort.vo;

import java.io.Serializable;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2024/1/9
 */
@Data
public class CombinationScopeVO implements Serializable {

    /**
     * 活动范围类型
     * @see net.xianmu.marketing.center.common.enums.activity.ScopeTypeEnum
     */
    private Integer scopeType;

    /**
     * 生效范围id
     */
    private Long scopeId;

    /**
     * 生效范围名称
     */
    private String scopeName;

}
