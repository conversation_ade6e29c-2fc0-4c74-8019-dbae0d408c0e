package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2022/12/1
 */
@Data
public class ActivityItemConfigInput implements Serializable {

    private Long id;

    /**
     * 基本信息id
     */
    private Long basicInfoId;

    /**
     * 商品选择方式，0 sku，1 类目，2 全部上架sku，3 标签商品，4 其他
     */
    private Integer goodSelectWay;

    /**
     * 商品定价类型，0 单品定价，1 满金额阶梯，2 满金额重复，3 满数量重复，4 满数量阶梯，5 其他
     */
    private Integer pricingType;

    /**
     * 商品定价类型扩展，目前主要是满减、满返的具体规则
     */
    private String pricingTypeExt;

    /**
     * 每件商品优惠幅度等于毛利的百分之X
     */
    private Integer discountPercentage;

    /**
     * 每件商品最多优惠X元
     */
    private BigDecimal discount;

    /**
     * sku配置
     */
    @Valid
    private List<ActivitySkuDetailInput> skuDetailList;

    /**
     * 类目或者标签配置
     */
    private List<ActivityItemDetailInput> itemDetailList;

    /**
     * 是否开启自动定价  1：开启    0：关闭
     */
    private Integer autoPrice;

    private Long updaterId;

    /**
     * 创建人系统来源  0-srm, 2-admin
     */
    private Integer systemOrigin;

}
