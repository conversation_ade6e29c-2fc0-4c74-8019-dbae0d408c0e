package net.xianmu.marketing.center.application.inbound.controller.user.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2024/1/9
 */
@Data
public class CurrentUserInfo implements Serializable {

    /**
     * user_auth表ID
     */
    private Long autUserId;

    /**
     * 登录用的账户
     */
    private String username;

    /**
     * 用户名称
     */
    private String nickname;
    /**
     *  手机号
     */
    private String phone;
    /**
     * user_base 表id
     */
    private Long authUserBaseId;
    /**
     * 各个业务表字段id
     * 如：admin-admin_id，merchant-m_Id
     */
    private Long bizUserId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 系统来源
     */
    private String systemOrigin;

    /**
     * 系统来源
     * @see net.xianmu.common.enums.base.auth.SystemOriginEnum
     */
    private Integer systemOriginType;


    // ------------------------     一些业务线独有的信息    ---------------------

    /**
     * supplier表ID：仅当systemOrigin=0时有值
     */
    private Long supplierId;

    /**
     * supplier表名称：仅当systemOrigin=0时有值
     */
    private String supplierName;


}
