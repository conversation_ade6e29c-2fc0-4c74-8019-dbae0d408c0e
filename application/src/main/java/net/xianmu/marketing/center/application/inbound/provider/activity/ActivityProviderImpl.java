package net.xianmu.marketing.center.application.inbound.provider.activity;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.activity.provide.ActivityProvide;
import net.xianmu.marketing.center.client.activity.req.ActivityInfoByAreaNoReq;
import net.xianmu.marketing.center.client.activity.req.ActivityInfoBySupplierReq;
import net.xianmu.marketing.center.client.activity.resp.ActivityInfoByAreaNoResp;
import net.xianmu.marketing.center.client.activity.resp.ActivityInfoBySupplierResp;
import net.xianmu.marketing.center.common.enums.activity.ActivityStatusEnum;
import net.xianmu.marketing.center.common.enums.activity.ActivityTypeEnum;
import net.xianmu.marketing.center.common.enums.activity.ScopeTypeEnum;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemScopeEntity;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.BatchCheckQueryParam;
import net.xianmu.marketing.center.domain.activity.param.ScopeQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivityBasicInfoQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailQueryRepository;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/16 13:39
 * @PackageName:net.xianmu.marketing.center.application.inbound.provider.activity
 * @ClassName: ActivityProviderImpl
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@Component
@DubboService
public class ActivityProviderImpl implements ActivityProvide {

    @Resource
    private ActivityBasicInfoQueryRepository basicInfoQueryRepository;

    @Resource
    private ActivitySkuDetailQueryRepository skuDetailQueryRepository;

    @Override
    public DubboResponse<List<ActivityInfoBySupplierResp>> getActivityInfoBySupplier(@Valid ActivityInfoBySupplierReq activityInfoBySupplierReq) {
        log.info("ActivityProvide[]getActivityInfoBySupplier[]start[]activityInfoBySupplierReq:{}", JSON.toJSONString(activityInfoBySupplierReq));
        BatchCheckQueryParam param = new BatchCheckQueryParam();
        param.setSystemOrigin(SystemOriginEnum.SRM.getType());
        param.setOwnerId(activityInfoBySupplierReq.getOwnerId());
        param.setStartTime(LocalDateTime.now());
        param.setType(ActivityTypeEnum.SPECIAL_PRICE.getCode());
        if (Objects.equals(param.getIsPermanent(), 1)) {
            param.setStartTime(LocalDateTime.now());
            param.setEndTime(LocalDateTime.of(2099, 12, 31, 23, 59, 59));
        }
        List<ScopeQueryParam> scopeList = new ArrayList<>();
        List<String> skus = new ArrayList<>();
        activityInfoBySupplierReq.getActivitySkuInfoReqs().forEach(activitySkuInfoReq -> {
            ScopeQueryParam scopeQueryParam = new ScopeQueryParam();
            scopeQueryParam.setScopeType(ScopeTypeEnum.WAREHOUSE.getCode());
            scopeQueryParam.setScopeId(activitySkuInfoReq.getWarehouseNo().longValue());
            scopeList.add(scopeQueryParam);
            skus.add(activitySkuInfoReq.getSku());
        });
        param.setScopeList(scopeList);
        List<ActivityItemScopeEntity> validActivityList = basicInfoQueryRepository.selectByScope(param);

        //根据活动信息+sku信息过滤活动信息
        //validActivityList有可能是configId不一样，所以转map需要处理下，防止重复
        Map<Long, ActivityItemScopeEntity> itemScopeEntityMap = validActivityList.stream()
                .collect(Collectors.toMap(ActivityItemScopeEntity::getItemConfigId, Function.identity(), (x, y) -> x));
        if (CollectionUtil.isEmpty(validActivityList)) {
            log.info("获取特价活动失败，特价活动信息为空！param:{}", JSON.toJSONString(param));
            return DubboResponse.getOK(Collections.emptyList());
        }

        List<Long> itemConfigIds = Lists.newArrayList(itemScopeEntityMap.keySet());

        //获取存在活动中的重复sku
        List<ActivitySkuDetailEntity> skuDetailList = skuDetailQueryRepository.listByItemConfigsSkus(itemConfigIds, skus);
        if (CollectionUtil.isEmpty(skuDetailList)) {
            log.info("获取特价活动失败，特价活动商品信息为空！itemConfigIds:{}, skus:{}", itemConfigIds, skus);
            return DubboResponse.getOK(Collections.emptyList());
        }

        List<ActivityInfoBySupplierResp> activityInfoBySupplierResps = new ArrayList<>();
        Map<String, Long> skuDetailMap = skuDetailList.stream().collect(Collectors.toMap(x -> x.getSku(), x -> x.getItemConfigId(), (x, y) -> x));
        skuDetailMap.keySet().stream().forEach(sku -> {
            Long itemConfigId = skuDetailMap.get(sku);
            ActivityItemScopeEntity activityItemScopeEntity = itemScopeEntityMap.get(itemConfigId);
            if (activityItemScopeEntity == null) {
                log.info("获取特价活动失败，商品sku不存在对应的活动！sku:{}, itemConfigId:{}", sku, itemConfigId);
                return;
            }

            ActivityInfoBySupplierResp activityInfoBySupplierResp = new ActivityInfoBySupplierResp();
            activityInfoBySupplierResp.setOwnerId(activityInfoBySupplierReq.getOwnerId());
            activityInfoBySupplierResp.setSku(sku);
            activityInfoBySupplierResp.setWarehouseNo(activityItemScopeEntity.getScopeId().intValue());
            activityInfoBySupplierResp.setActivityId(activityItemScopeEntity.getBasicInfoId());
            if (Objects.equals(activityItemScopeEntity.getStatus(), ActivityStatusEnum.PENDING_REVIEW.getCode())) {
                activityInfoBySupplierResp.setActivityStatus(ActivityStatusEnum.PENDING_REVIEW.getCode());
            } else if (Objects.equals(activityItemScopeEntity.getIsPermanent(), CommonStatus.YES.getCode())) {
                activityInfoBySupplierResp.setActivityStatus(ActivityStatusEnum.EFFECTING.getCode());
            } else if (LocalDateTime.now().isBefore(activityItemScopeEntity.getStartTime())) {
                activityInfoBySupplierResp.setActivityStatus(ActivityStatusEnum.NOT_VALID.getCode());
            }
            activityInfoBySupplierResps.add(activityInfoBySupplierResp);
        });
        return DubboResponse.getOK(activityInfoBySupplierResps);
    }

    @Override
    public DubboResponse<List<ActivityInfoByAreaNoResp>> getActivityInfoByAreaNo(@Valid ActivityInfoByAreaNoReq activityInfoByAreaNoReq) {
        return null;
    }
}
