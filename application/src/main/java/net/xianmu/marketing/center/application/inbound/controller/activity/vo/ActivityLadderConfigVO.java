package net.xianmu.marketing.center.application.inbound.controller.activity.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/5/14 14:34
 */
@Data
public class ActivityLadderConfigVO implements Serializable {

    /**
     * 阶梯数
     */
    private Integer unit;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整（定价方式）
     */
    private Integer roundingMode;

    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    private BigDecimal amount;
}
