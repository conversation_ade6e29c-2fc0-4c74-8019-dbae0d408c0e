package net.xianmu.marketing.center.application.inbound.controller.activity.assembler;


import net.xianmu.marketing.center.application.inbound.controller.activity.input.ActivityBasicInfoInput;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.ActivityBasicInfoUpdateInput;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.ActivityInsertInput;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.ActivityBasicInfoVO;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.ActivityPageRespVO;
import net.xianmu.marketing.center.common.enums.activity.ActivityAuditStatusEnum;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.dto.ActivityPageRespDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityBasicInfoEntity;

import net.xianmu.marketing.center.domain.activity.param.command.ActivityBasicInfoCommandParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicInfoQueryParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;

/**
 *
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
public class ActivityBasicInfoAssembler {

    private ActivityBasicInfoAssembler() {
        // 无需实现
    }

    public static ActivityBasicInfoCommandParam buildCreateParam(ActivityBasicInfoInput activityBasicInfoCommandInput) {
        if (activityBasicInfoCommandInput == null) {
            return null;
        }
        ActivityBasicInfoCommandParam activityBasicInfoCommandParam = new ActivityBasicInfoCommandParam();
        activityBasicInfoCommandParam.setId(activityBasicInfoCommandInput.getBasicInfoId());
        activityBasicInfoCommandParam.setName(activityBasicInfoCommandInput.getName());
        activityBasicInfoCommandParam.setStartTime(activityBasicInfoCommandInput.getStartTime());
        activityBasicInfoCommandParam.setEndTime(activityBasicInfoCommandInput.getEndTime());
        activityBasicInfoCommandParam.setIsPermanent(activityBasicInfoCommandInput.getIsPermanent());
        activityBasicInfoCommandParam.setStatus(activityBasicInfoCommandInput.getStatus());
        activityBasicInfoCommandParam.setNeedPre(activityBasicInfoCommandInput.getNeedPre());
        activityBasicInfoCommandParam.setPreStartTime(activityBasicInfoCommandInput.getPreStartTime());
        activityBasicInfoCommandParam.setPreEndTime(activityBasicInfoCommandInput.getPreEndTime());
        activityBasicInfoCommandParam.setType(activityBasicInfoCommandInput.getType());
        activityBasicInfoCommandParam.setTag(activityBasicInfoCommandInput.getTag());
        activityBasicInfoCommandParam.setRemark(activityBasicInfoCommandInput.getRemark());
        activityBasicInfoCommandParam.setCreatorId(activityBasicInfoCommandInput.getCreatorId());
        activityBasicInfoCommandParam.setUpdaterId(activityBasicInfoCommandInput.getUpdaterId());
        activityBasicInfoCommandParam.setDelFlag(CommonStatus.NO.getCode());
        activityBasicInfoCommandParam.setCreateTime(activityBasicInfoCommandInput.getCreateTime());
        activityBasicInfoCommandParam.setUpdateTime(activityBasicInfoCommandInput.getUpdateTime());
        activityBasicInfoCommandParam.setOwnerId(activityBasicInfoCommandInput.getOwnerId());
        activityBasicInfoCommandParam.setSystemOrigin(activityBasicInfoCommandInput.getSystemOrigin());
        activityBasicInfoCommandParam.setAuditStatus(ActivityAuditStatusEnum.APPROVED.getCode());
        return activityBasicInfoCommandParam;
    }


    public static ActivityBasicInfoCommandParam buildUpdateParam(ActivityBasicInfoUpdateInput activityBasicInfoCommandInput) {
        if (activityBasicInfoCommandInput == null) {
            return null;
        }
        ActivityBasicInfoCommandParam activityBasicInfoCommandParam = new ActivityBasicInfoCommandParam();
        activityBasicInfoCommandParam.setId(activityBasicInfoCommandInput.getBasicInfoId());
        activityBasicInfoCommandParam.setName(activityBasicInfoCommandInput.getName());
        activityBasicInfoCommandParam.setStartTime(activityBasicInfoCommandInput.getStartTime());
        activityBasicInfoCommandParam.setEndTime(activityBasicInfoCommandInput.getEndTime());
        activityBasicInfoCommandParam.setIsPermanent(activityBasicInfoCommandInput.getIsPermanent());
        activityBasicInfoCommandParam.setStatus(activityBasicInfoCommandInput.getStatus());
        activityBasicInfoCommandParam.setNeedPre(activityBasicInfoCommandInput.getNeedPre());
        activityBasicInfoCommandParam.setPreStartTime(activityBasicInfoCommandInput.getPreStartTime());
        activityBasicInfoCommandParam.setPreEndTime(activityBasicInfoCommandInput.getPreEndTime());
        activityBasicInfoCommandParam.setType(activityBasicInfoCommandInput.getType());
        activityBasicInfoCommandParam.setTag(activityBasicInfoCommandInput.getTag());
        activityBasicInfoCommandParam.setRemark(activityBasicInfoCommandInput.getRemark());
        if (activityBasicInfoCommandInput.getUpdaterId() != null) {
            activityBasicInfoCommandParam.setUpdaterId(activityBasicInfoCommandInput.getUpdaterId().intValue());
        }
        activityBasicInfoCommandParam.setDelFlag(activityBasicInfoCommandInput.getDelFlag());
        activityBasicInfoCommandParam.setCreateTime(activityBasicInfoCommandInput.getCreateTime());
        activityBasicInfoCommandParam.setUpdateTime(activityBasicInfoCommandInput.getUpdateTime());
        activityBasicInfoCommandParam.setOwnerId(activityBasicInfoCommandInput.getOwnerId());
        activityBasicInfoCommandParam.setSystemOrigin(activityBasicInfoCommandInput.getSystemOrigin());
        activityBasicInfoCommandParam.setAuditStatus(activityBasicInfoCommandInput.getAuditStatus());
        activityBasicInfoCommandParam.setAuditUserId(activityBasicInfoCommandInput.getAuditUserId());
        activityBasicInfoCommandParam.setAuditRemark(activityBasicInfoCommandInput.getAuditRemark());
        return activityBasicInfoCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<ActivityBasicInfoVO> toActivityBasicInfoVOList(List<ActivityBasicInfoEntity> activityBasicInfoEntityList) {
        if (activityBasicInfoEntityList == null) {
            return Collections.emptyList();
        }
        List<ActivityBasicInfoVO> activityBasicInfoVOList = new ArrayList<>();
        for (ActivityBasicInfoEntity activityBasicInfoEntity : activityBasicInfoEntityList) {
            activityBasicInfoVOList.add(toActivityBasicInfoVO(activityBasicInfoEntity));
        }
        return activityBasicInfoVOList;
}


   public static ActivityBasicInfoVO toActivityBasicInfoVO(ActivityBasicInfoEntity activityBasicInfoEntity) {
       if (activityBasicInfoEntity == null) {
            return null;
       }
       ActivityBasicInfoVO activityBasicInfoVO = new ActivityBasicInfoVO();
       activityBasicInfoVO.setBasicInfoId(activityBasicInfoEntity.getId());
       activityBasicInfoVO.setName(activityBasicInfoEntity.getName());
       activityBasicInfoVO.setStartTime(activityBasicInfoEntity.getStartTime());
       activityBasicInfoVO.setEndTime(activityBasicInfoEntity.getEndTime());
       activityBasicInfoVO.setIsPermanent(activityBasicInfoEntity.getIsPermanent());
       activityBasicInfoVO.setStatus(activityBasicInfoEntity.getStatus());
       activityBasicInfoVO.setNeedPre(activityBasicInfoEntity.getNeedPre());
       activityBasicInfoVO.setPreStartTime(activityBasicInfoEntity.getPreStartTime());
       activityBasicInfoVO.setPreEndTime(activityBasicInfoEntity.getPreEndTime());
       activityBasicInfoVO.setType(activityBasicInfoEntity.getType());
       activityBasicInfoVO.setTag(activityBasicInfoEntity.getTag());
       activityBasicInfoVO.setRemark(activityBasicInfoEntity.getRemark());
       activityBasicInfoVO.setCreatorId(activityBasicInfoEntity.getCreatorId());
       activityBasicInfoVO.setUpdaterId(activityBasicInfoEntity.getUpdaterId());
       activityBasicInfoVO.setDelFlag(activityBasicInfoEntity.getDelFlag());
       activityBasicInfoVO.setCreateTime(activityBasicInfoEntity.getCreateTime());
       activityBasicInfoVO.setUpdateTime(activityBasicInfoEntity.getUpdateTime());
       activityBasicInfoVO.setOwnerId(activityBasicInfoEntity.getOwnerId());
       activityBasicInfoVO.setSystemOrigin(activityBasicInfoEntity.getSystemOrigin());
       activityBasicInfoVO.setAuditStatus(activityBasicInfoEntity.getAuditStatus());
       activityBasicInfoVO.setAuditUserId(activityBasicInfoEntity.getAuditUserId());
       activityBasicInfoVO.setAuditRemark(activityBasicInfoEntity.getAuditRemark());
       activityBasicInfoVO.setAuditTime(activityBasicInfoEntity.getAuditTime());
       return activityBasicInfoVO;
   }

    public static ActivityPageRespVO toActivityBasicInfoVO(ActivityPageRespDTO dto) {
        if (dto == null) {
            return null;
        }
        ActivityPageRespVO vo = new ActivityPageRespVO();
        vo.setBasicInfoId(dto.getBasicInfoId());
        vo.setName(dto.getName());
        vo.setStartTime(dto.getStartTime());
        vo.setEndTime(dto.getEndTime());
        vo.setIsPermanent(dto.getIsPermanent());
        vo.setActivityStatus(dto.getActivityStatus());
        vo.setStatus(dto.getStatus());
        vo.setType(dto.getType());
        vo.setTag(dto.getTag());
        vo.setRemark(dto.getRemark());
        vo.setCreatorId(dto.getCreatorId());
        vo.setCreatorName(dto.getCreatorName());
        vo.setUpdaterId(dto.getUpdaterId());
        vo.setDelFlag(dto.getDelFlag());
        vo.setCreateTime(dto.getCreateTime());
        vo.setUpdateTime(dto.getUpdateTime());
        vo.setScopeType(dto.getScopeType());
        vo.setTotalScope(dto.getTotalScope());
        // vo.setScopeConfigs(toActivityScopeConfigVOList(dto.getScopeConfigs()));
        vo.setScopeNameList(dto.getScopeNameList());
        // vo.setSkuNum(dto.getSkuNum());
        return vo;
    }

}
