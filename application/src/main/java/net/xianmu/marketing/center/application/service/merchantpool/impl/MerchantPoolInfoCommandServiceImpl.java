package net.xianmu.marketing.center.application.service.merchantpool.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.shade.scala.annotation.meta.param;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.assembler.MerchantPoolInfoAssembler;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.dto.MerchantPoolDetailImportDTO;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.dto.MerchantPoolUploadSkuImportDTO;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.dto.ProductInfoDTO;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.input.command.MerchantPoolInfoInsertCommandInput;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.input.command.MerchantPoolInfoUpdateCommandInput;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.input.command.MerchantPoolInfoUploadSkuCommandInput;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.input.query.MerchantPoolInfoQueryInput;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.vo.MerchantPoolInfoVO;
import net.xianmu.marketing.center.application.inbound.controller.merchantpool.vo.MerchantPoolUploadSkuVO;
import net.xianmu.marketing.center.application.service.merchantpool.MerchantPoolDetailCommandService;
import net.xianmu.marketing.center.application.service.merchantpool.MerchantPoolInfoCommandService;
import net.xianmu.marketing.center.application.service.merchantpool.MerchantPoolInfoQueryService;
import net.xianmu.marketing.center.common.constant.CommonRedisKey;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.common.enums.merchantpool.CreateWayEnum;
import net.xianmu.marketing.center.common.enums.merchantpool.DataStatusEnum;
import net.xianmu.marketing.center.common.enums.merchantpool.UpdateWayEnum;
import net.xianmu.marketing.center.domain.merchantpool.entity.MerchantPoolInfoEntity;
import net.xianmu.marketing.center.domain.merchantpool.param.command.MerchantPoolDetailCommandParam;
import net.xianmu.marketing.center.domain.merchantpool.param.command.MerchantPoolInfoCommandParam;
import net.xianmu.marketing.center.domain.merchantpool.param.command.MerchantPoolRuleDetailCommandParam;
import net.xianmu.marketing.center.domain.merchantpool.service.MerchantPoolDetailCommandDomainService;
import net.xianmu.marketing.center.domain.merchantpool.service.MerchantPoolInfoCommandDomainService;
import net.xianmu.marketing.center.domain.merchantpool.service.MerchantPoolRuleDetailCommandDomainService;
import net.xianmu.marketing.center.facade.item.ItemQueryFacade;
import net.xianmu.marketing.center.facade.item.dto.SkuInfoDetailDTO;
import net.xianmu.marketing.center.facade.item.input.BatchSkuInput;
import net.xianmu.marketing.center.facade.merchant.MerchantQueryFacade;
import net.xianmu.marketing.center.facade.merchant.dto.MerchantQueryDTO;
import net.xianmu.marketing.center.facade.merchant.input.MerchantQueryInput;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-12-16 15:42:54
* @version 1.0
*
*/
@Service
@Slf4j
public class MerchantPoolInfoCommandServiceImpl implements MerchantPoolInfoCommandService {

    @Autowired
    private MerchantPoolInfoCommandDomainService merchantPoolInfoCommandDomainService;

    @Autowired
    private MerchantPoolRuleDetailCommandDomainService merchantPoolRuleDetailCommandDomainService;

    @Autowired
    private MerchantPoolDetailCommandDomainService merchantPoolDetailCommandDomainService;

    @Autowired
    private MerchantQueryFacade merchantQueryFacade;

    @Autowired
    private MerchantPoolInfoQueryService merchantPoolInfoQueryService;

    @Autowired
    private ItemQueryFacade itemQueryFacade;

    @Autowired
    private MerchantPoolDetailCommandService merchantPoolDetailCommandService;

    @Autowired
    @Lazy
    private MerchantPoolInfoCommandService selfService;

    private static final int MAX_SIZE = 500;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(prefixKey = CommonRedisKey.MerchantPoolLock.MERCHANT_POOL_INSERT, key = "{input.creator}")
    public Boolean insert(MerchantPoolInfoInsertCommandInput input) {
        //保存圈人的基本信息
        MerchantPoolInfoCommandParam param = MerchantPoolInfoAssembler.buildCreateParamV2(input);
        param.setStatus(DataStatusEnum.INITIALIZING.getCode());
        param.setVersion(0);
        param.setCreateTime(LocalDateTime.now());
        param.setDataSource(CommonStatus.YES.getCode());
        MerchantPoolInfoEntity insert = merchantPoolInfoCommandDomainService.insert(param);

        //保存圈人规则明细
        MerchantPoolRuleDetailCommandParam detailCommandParam = new MerchantPoolRuleDetailCommandParam();
        detailCommandParam.setPoolInfoId(insert.getId());
        detailCommandParam.setRuleDetail(input.getRuleDetail());
        if (Objects.equals(CreateWayEnum.EXCEL.getCode(), input.getCreateWay()) && !StringUtils.isEmpty(input.getFileUrl())
                && !StringUtils.isEmpty(input.getFileName())) {
            Map<String, String> map = new HashMap<>(2);
            map.put("fileName", input.getFileName());
            map.put("fileUrl", input.getFileUrl());
            detailCommandParam.setRuleDetail(JSON.toJSONString(map));
        }
        detailCommandParam.setCreateTime(LocalDateTime.now());
        merchantPoolRuleDetailCommandDomainService.insert(detailCommandParam);

        //Excel导入商家信息 在线圈选走定时任务T+1更新
        if (Objects.equals(CreateWayEnum.EXCEL.getCode(), input.getCreateWay())) {
            if (StringUtils.isEmpty(input.getKey())) {
                log.info("excel导入方式，上传excel为空！");
                return Boolean.TRUE;
            }

            //解析文件数据
            InputStream inputStream = OssGetUtil.getInputStream(input.getKey());
            List<MerchantPoolDetailImportDTO> merchantPoolDetailImportDTOS;
            try {
                merchantPoolDetailImportDTOS = EasyExcel.read(inputStream,
                        MerchantPoolDetailImportDTO.class, null).doReadAllSync();
            } catch (Exception e) {
                throw new BizException("上传人群包模版解析异常！");
            }

            if (CollectionUtil.isEmpty(merchantPoolDetailImportDTOS)) {
                throw new BizException("上传人群包模版数据为空！");
            }
            selfService.insertPoolDetail(insert.getId(), merchantPoolDetailImportDTOS);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(MerchantPoolInfoUpdateCommandInput input) {
        MerchantPoolInfoVO detail = merchantPoolInfoQueryService.getDetail(input.getId());
        if (detail == null) {
            throw new BizException("未找到该人群包！");
        }

        //老的圈人数据不支持修改
        if (Objects.equals(CommonStatus.NO.getCode(), detail.getDataSource()) &&
                Objects.equals(CreateWayEnum.ONLINE_RULE.getCode(), detail.getCreateWay())) {
            throw new BizException("老的在线圈选数据不支持修改，请新建圈人规则！");
        }

        //更新基础信息
        MerchantPoolInfoCommandParam param = new MerchantPoolInfoCommandParam();
        param.setUpdater(input.getUpdater());
        param.setId(input.getId());
        param.setName(input.getName());
        param.setRemark(input.getRemark());
        param.setUpdateTime(LocalDateTime.now());

        if (input.getNotModifyRule() != null && input.getNotModifyRule()) {
            param.setDataSource(CommonStatus.YES.getCode());
            param.setStatus(DataStatusEnum.INITIALIZING.getCode());

            //更新规则明细
            MerchantPoolRuleDetailCommandParam detailCommandParam = new MerchantPoolRuleDetailCommandParam();
            detailCommandParam.setPoolInfoId(input.getId());
            detailCommandParam.setRuleDetail(input.getRuleDetail());
            if (Objects.equals(CreateWayEnum.EXCEL.getCode(), detail.getCreateWay()) && !StringUtils.isEmpty(input.getFileUrl())
                    && !StringUtils.isEmpty(input.getFileName()) && !StringUtils.isEmpty(input.getKey())) {
                Map<String, String> map = new HashMap<>(2);
                map.put("fileName", input.getFileName());
                map.put("fileUrl", input.getFileUrl());
                detailCommandParam.setRuleDetail(JSON.toJSONString(map));
            }
            detailCommandParam.setUpdateTime(LocalDateTime.now());
            merchantPoolRuleDetailCommandDomainService.updateByInfoId(detailCommandParam);

            //Excel导入商家信息 在线圈选走定时任务T+1更新
            if (Objects.equals(CreateWayEnum.EXCEL.getCode(), detail.getCreateWay()) && !StringUtils.isEmpty(input.getKey())) {
                //解析文件数据
                InputStream inputStream = OssGetUtil.getInputStream(input.getKey());
                List<MerchantPoolDetailImportDTO> merchantPoolDetailImportDTOS;
                try {
                    merchantPoolDetailImportDTOS = EasyExcel.read(inputStream,
                            MerchantPoolDetailImportDTO.class, null).doReadAllSync();
                } catch (Exception e) {
                    throw new BizException("上传人群包模版解析异常！");
                }

                if (CollectionUtil.isEmpty(merchantPoolDetailImportDTOS)) {
                    throw new BizException("上传人群包模版数据为空！");
                }
                selfService.insertPoolDetail(input.getId(), merchantPoolDetailImportDTOS);
            }
        }
        merchantPoolInfoCommandDomainService.update(param);
        return Boolean.TRUE;
    }

    @Override
    public int delete(Long id) {
        return merchantPoolInfoCommandDomainService.delete(id);
    }

    @Override
    public MerchantPoolUploadSkuVO batchUploadSku(MerchantPoolInfoUploadSkuCommandInput input) {
        //解析文件数据
        InputStream inputStream = OssGetUtil.getInputStream(input.getKey());
        List<MerchantPoolUploadSkuImportDTO> merchantPoolUploadSkuImportDTOS;
        try {
            merchantPoolUploadSkuImportDTOS = EasyExcel.read(inputStream,
                    MerchantPoolUploadSkuImportDTO.class, null).doReadAllSync();
        } catch (Exception e) {
            throw new BizException("圈人规则批量上传商品模版解析异常！");
        }
        if (CollectionUtil.isEmpty(merchantPoolUploadSkuImportDTOS)) {
            throw new BizException("圈人规则批量上传商品模版数据为空！");
        }
        Set<String> skuSet = merchantPoolUploadSkuImportDTOS.stream().map(MerchantPoolUploadSkuImportDTO::getSku)
                .collect(Collectors.toSet());
        BatchSkuInput skuInput = new BatchSkuInput();
        skuInput.setSkus(new ArrayList<>(skuSet));
        List<SkuInfoDetailDTO> detailDTOS = itemQueryFacade.listBySkusNew(skuInput);
        if (CollectionUtil.isEmpty(detailDTOS)) {
            throw new BizException("圈人规则批量上传商品有效数据为空！");
        }

        Map<String, SkuInfoDetailDTO> inventoryVOMap = detailDTOS.stream().collect(Collectors.toMap(SkuInfoDetailDTO::getSku,
                Function.identity()));
        MerchantPoolUploadSkuVO merchantPoolUploadSkuVO = new MerchantPoolUploadSkuVO();
        List<String> failedSkus = new ArrayList<>();
        Set<String> skus = new HashSet<>();
        List<ProductInfoDTO> productInfoDTOS = new ArrayList<>();
        merchantPoolUploadSkuImportDTOS.forEach(item -> {
            if (skus.contains(item.getSku())) {
                return;
            }
            if (inventoryVOMap.containsKey(item.getSku())) {
                SkuInfoDetailDTO inventoryVO = inventoryVOMap.get(item.getSku());
                ProductInfoDTO productInfoDTO = new ProductInfoDTO();
                productInfoDTO.setSku(item.getSku());
                productInfoDTO.setProductName(inventoryVO.getPdName());
                productInfoDTO.setWeight(inventoryVO.getWeight());
                productInfoDTOS.add(productInfoDTO);
            } else {
                failedSkus.add(item.getSku());
            }
            skus.add(item.getSku());
        });
        merchantPoolUploadSkuVO.setProductInfoDTOS(productInfoDTOS);
        merchantPoolUploadSkuVO.setFailedSkus(failedSkus);
        return merchantPoolUploadSkuVO;
    }

    @Async("asyncExecutor")
    @Override
    public void insertPoolDetail(Long id, List<MerchantPoolDetailImportDTO> merchantPoolDetailImportDTOS) {
        log.info("异步批量导入商家信息开始！");
        try {
            //删除老的圈人数据
            int delete = merchantPoolDetailCommandDomainService.deleteByPoolInfoId(id);
            log.info("删除人群包ID:{}的圈人数据成功，delete:{}", id, delete);

            List<List<MerchantPoolDetailImportDTO>> partition = Lists.partition(merchantPoolDetailImportDTOS, MAX_SIZE);
            for (List<MerchantPoolDetailImportDTO> poolDetailImportDTOS : partition) {
                Set<Long> mIds = poolDetailImportDTOS.stream().map(MerchantPoolDetailImportDTO::getMId).collect(Collectors.toSet());
                MerchantQueryInput merchantQueryInput = new MerchantQueryInput();
                merchantQueryInput.setMIds(new ArrayList<>(mIds));
                List<MerchantQueryDTO> merchantQueryDTOS = merchantQueryFacade.batchGetMerchant(merchantQueryInput);
                if (CollectionUtil.isEmpty(merchantQueryDTOS)) {
                    log.info("查询当前客户数量为空！merchantQueryInput:{}", JSON.toJSONString(merchantQueryInput));
                    continue;
                }
                List<MerchantPoolDetailCommandParam> poolDetailCommandParams = new ArrayList<>();
                merchantQueryDTOS.forEach(e -> {
                    MerchantPoolDetailCommandParam merchantPoolDetailCommandParam = new MerchantPoolDetailCommandParam();
                    merchantPoolDetailCommandParam.setPoolInfoId(id);
                    merchantPoolDetailCommandParam.setVersion(0);
                    merchantPoolDetailCommandParam.setMId(e.getMId());
                    merchantPoolDetailCommandParam.setSize(e.getSize());
                    merchantPoolDetailCommandParam.setAreaNo(e.getAreaNo());
                    merchantPoolDetailCommandParam.setCreateTime(LocalDateTime.now());
                    poolDetailCommandParams.add(merchantPoolDetailCommandParam);
                });
                merchantPoolDetailCommandDomainService.batchInsert(poolDetailCommandParams);
            }
            MerchantPoolInfoCommandParam updateParam = new MerchantPoolInfoCommandParam();
            updateParam.setId(id);
            updateParam.setStatus(DataStatusEnum.SUCCESS.getCode());
            updateParam.setUpdateTime(LocalDateTime.now());
            merchantPoolInfoCommandDomainService.update(updateParam);
            log.info("导入人群包数据成功！id:{}", id);
        } catch (Exception e) {
            log.error("导入人群包数据异常！id:{}", id, e);
            MerchantPoolInfoCommandParam updateParam = new MerchantPoolInfoCommandParam();
            updateParam.setId(id);
            updateParam.setStatus(DataStatusEnum.FAIL.getCode());
            updateParam.setUpdateTime(LocalDateTime.now());
            merchantPoolInfoCommandDomainService.update(updateParam);
        }
        log.info("异步批量导入商家信息结束！");
    }

    @Override
    @XmLock(prefixKey = CommonRedisKey.MerchantPoolLock.MERCHANT_POOL_UPDATE_DATA, key = "{input.id}")
    public Boolean updateMerchantInfoData(MerchantPoolInfoQueryInput input) {
        MerchantPoolInfoVO poolInfo = merchantPoolInfoQueryService.getDetail(input.getId());
        if (poolInfo == null) {
            throw new BizException("圈人规则不存在！");
        }
        if (Objects.equals(poolInfo.getDataSource(), CommonStatus.NO.getCode())) {
            throw new BizException("非数仓计算的圈人暂不支持手动更新！");
        }

        //非在线圈人不支持手动更新
        if (!Objects.equals(poolInfo.getCreateWay(), CreateWayEnum.ONLINE_RULE.getCode())) {
            throw new BizException("非在线圈人不支持手动更新！");
        }

        //自动更新，非失败的不支持手动更新
        if (Objects.equals(poolInfo.getUpdateWay(), UpdateWayEnum.AUTO.getCode())
                && !DataStatusEnum.isFailed(poolInfo.getStatus())) {
            throw new BizException("自动更新，非失败的不支持手动更新！");
        }

        //异步刷新数据
        selfService.autoUpdateMerchantPool(input.getId());
        return Boolean.TRUE;
    }

    @Override
    @Async("asyncExecutor")
    public void autoUpdateMerchantPool(Long poolInfoId) {
        log.info("异步自动更新商家信息开始！poolInfoId:{}", poolInfoId);
        merchantPoolDetailCommandService.autoUpdateMerchantPool(poolInfoId);
        log.info("异步自动更新商家信息结束！poolInfoId:{}", poolInfoId);
    }
}