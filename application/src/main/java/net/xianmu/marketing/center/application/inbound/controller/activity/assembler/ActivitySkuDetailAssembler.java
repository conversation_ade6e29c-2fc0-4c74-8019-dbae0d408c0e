package net.xianmu.marketing.center.application.inbound.controller.activity.assembler;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.ActivitySkuDetailInput;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.ActivityLadderConfigVO;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.ActivitySkuDetailVO;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.entity.ActivitySkuDetailEntity;
import net.xianmu.marketing.center.domain.activity.param.command.ActivitySkuDetailCommandParam;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
public class ActivitySkuDetailAssembler {

    private ActivitySkuDetailAssembler() {
        // 无需实现
    }

    /**
     * 剩余库存默认值
     */
    public static final int DEFAULT_ACTUAL_QUANTITY = 100000;


    public static ActivitySkuDetailCommandParam buildCreateParam(ActivitySkuDetailInput activitySkuDetailCommandInput, Long itemConfigId, Integer systemOrigin) {
        if (activitySkuDetailCommandInput == null) {
            return null;
        }
        ActivitySkuDetailCommandParam activitySkuDetailCommandParam = new ActivitySkuDetailCommandParam();
        activitySkuDetailCommandParam.setId(activitySkuDetailCommandInput.getId());
        activitySkuDetailCommandParam.setItemConfigId(itemConfigId);
        activitySkuDetailCommandParam.setSku(activitySkuDetailCommandInput.getSku());
        activitySkuDetailCommandParam.setRoundingMode(activitySkuDetailCommandInput.getRoundingMode());
        activitySkuDetailCommandParam.setAdjustType(activitySkuDetailCommandInput.getAdjustType());
        activitySkuDetailCommandParam.setAmount(activitySkuDetailCommandInput.getAmount());
        activitySkuDetailCommandParam.setSort(activitySkuDetailCommandInput.getSort());
        activitySkuDetailCommandParam.setPlanQuantity(activitySkuDetailCommandInput.getPlanQuantity());
        activitySkuDetailCommandParam.setActualQuantity(activitySkuDetailCommandInput.getActualQuantity());
        activitySkuDetailCommandParam.setLockQuantity(activitySkuDetailCommandInput.getLockQuantity());
        activitySkuDetailCommandParam.setAccountLimit(activitySkuDetailCommandInput.getAccountLimit());
        activitySkuDetailCommandParam.setLimitQuantity(activitySkuDetailCommandInput.getLimitQuantity());
        activitySkuDetailCommandParam.setMinSaleNum(activitySkuDetailCommandInput.getMinSaleNum());
        activitySkuDetailCommandParam.setSingleDeposit(activitySkuDetailCommandInput.getSingleDeposit());
        activitySkuDetailCommandParam.setExpansionRatio(activitySkuDetailCommandInput.getExpansionRatio());
        activitySkuDetailCommandParam.setHidePrice(activitySkuDetailCommandInput.getHidePrice());
        activitySkuDetailCommandParam.setTimingConfig(activitySkuDetailCommandInput.getTimingConfig());
        activitySkuDetailCommandParam.setDelFlag(activitySkuDetailCommandInput.getDelFlag());
        activitySkuDetailCommandParam.setIsSupportTiming(activitySkuDetailCommandInput.getIsSupportTiming());
        activitySkuDetailCommandParam.setAutoPrice(activitySkuDetailCommandInput.getAutoPrice());
        activitySkuDetailCommandParam.setDiscountLabel(activitySkuDetailCommandInput.getDiscountLabel());
        activitySkuDetailCommandParam.setMarketingCostSharingRatio(activitySkuDetailCommandInput.getMarketingCostSharingRatio());
        if (!CollectionUtils.isEmpty(activitySkuDetailCommandInput.getActivityLadderConfigDTOList())) {
            activitySkuDetailCommandParam.setLadderConfig(JSON.toJSONString(activitySkuDetailCommandInput.getActivityLadderConfigDTOList()));
        }
        activitySkuDetailCommandParam.setMarketingCostSharingRatio(activitySkuDetailCommandInput.getMarketingCostSharingRatio());
        if (activitySkuDetailCommandInput.getLimitQuantity() == null) {
            activitySkuDetailCommandParam.setLimitQuantity(CommonStatus.NO.getCode());
        }
        if (activitySkuDetailCommandInput.getMinSaleNum() == null) {
            activitySkuDetailCommandParam.setMinSaleNum(CommonStatus.NO.getCode());
        }
        if (activitySkuDetailCommandInput.getActualQuantity() == null) {
            //设置剩余库存默认值
            activitySkuDetailCommandParam.setActualQuantity(DEFAULT_ACTUAL_QUANTITY);
        }
        if (activitySkuDetailCommandInput.getDiscountLabel() == null) {
            //默认展示特价标签
            activitySkuDetailCommandParam.setDiscountLabel(CommonStatus.YES.getCode());
        }
        if (activitySkuDetailCommandInput.getIsSupportTiming() == null) {
            //默认不支持省心送
            activitySkuDetailCommandParam.setIsSupportTiming(CommonStatus.NO.getCode());
        }
        if (Objects.equal(systemOrigin, SystemOriginEnum.SRM.getType())) {
            activitySkuDetailCommandParam.setAutoPrice(CommonStatus.NO.getCode());
            activitySkuDetailCommandParam.setIsSupportTiming(CommonStatus.NO.getCode());
            activitySkuDetailCommandParam.setDiscountLabel(CommonStatus.YES.getCode());
        }
        return activitySkuDetailCommandParam;
    }


// ------------------------------- response ----------------------------

    public static List<ActivitySkuDetailVO> toActivitySkuDetailVOList(List<ActivitySkuDetailEntity> activitySkuDetailEntityList) {
        if (activitySkuDetailEntityList == null) {
            return Collections.emptyList();
        }
        List<ActivitySkuDetailVO> activitySkuDetailVOList = new ArrayList<>();
        for (ActivitySkuDetailEntity activitySkuDetailEntity : activitySkuDetailEntityList) {
            activitySkuDetailVOList.add(toActivitySkuDetailVO(activitySkuDetailEntity));
        }
        return activitySkuDetailVOList;
}


   public static ActivitySkuDetailVO toActivitySkuDetailVO(ActivitySkuDetailEntity activitySkuDetailEntity) {
       if (activitySkuDetailEntity == null) {
            return null;
       }
       ActivitySkuDetailVO activitySkuDetailVO = new ActivitySkuDetailVO();
       activitySkuDetailVO.setId(activitySkuDetailEntity.getId());
       activitySkuDetailVO.setItemConfigId(activitySkuDetailEntity.getItemConfigId());
       activitySkuDetailVO.setSku(activitySkuDetailEntity.getSku());
       activitySkuDetailVO.setRoundingMode(activitySkuDetailEntity.getRoundingMode());
       activitySkuDetailVO.setAdjustType(activitySkuDetailEntity.getAdjustType());
       activitySkuDetailVO.setAmount(activitySkuDetailEntity.getAmount());
       activitySkuDetailVO.setSort(activitySkuDetailEntity.getSort());
       activitySkuDetailVO.setPlanQuantity(activitySkuDetailEntity.getPlanQuantity());
       activitySkuDetailVO.setActualQuantity(activitySkuDetailEntity.getActualQuantity());
       activitySkuDetailVO.setLockQuantity(activitySkuDetailEntity.getLockQuantity());
       activitySkuDetailVO.setAccountLimit(activitySkuDetailEntity.getAccountLimit());
       activitySkuDetailVO.setLimitQuantity(activitySkuDetailEntity.getLimitQuantity());
       activitySkuDetailVO.setMinSaleNum(activitySkuDetailEntity.getMinSaleNum());
       activitySkuDetailVO.setSingleDeposit(activitySkuDetailEntity.getSingleDeposit());
       activitySkuDetailVO.setExpansionRatio(activitySkuDetailEntity.getExpansionRatio());
       activitySkuDetailVO.setHidePrice(activitySkuDetailEntity.getHidePrice());
       activitySkuDetailVO.setTimingConfig(activitySkuDetailEntity.getTimingConfig());
       activitySkuDetailVO.setDelFlag(activitySkuDetailEntity.getDelFlag());
       activitySkuDetailVO.setCreateTime(activitySkuDetailEntity.getCreateTime());
       activitySkuDetailVO.setUpdateTime(activitySkuDetailEntity.getUpdateTime());
       activitySkuDetailVO.setIsSupportTiming(activitySkuDetailEntity.getIsSupportTiming());
       activitySkuDetailVO.setAutoPrice(activitySkuDetailEntity.getAutoPrice());
       activitySkuDetailVO.setDiscountLabel(activitySkuDetailEntity.getDiscountLabel());
       activitySkuDetailVO.setSkuName(activitySkuDetailEntity.getSkuName());
       activitySkuDetailVO.setLogo(activitySkuDetailEntity.getLogo());
       activitySkuDetailVO.setWeight(activitySkuDetailEntity.getWeight());
       activitySkuDetailVO.setUnit(activitySkuDetailEntity.getUnit());
       if (StringUtils.isNotBlank(activitySkuDetailEntity.getLadderConfig())) {
           List<ActivityLadderConfigVO> activityLadderConfigVOS = JSON.parseArray(activitySkuDetailEntity.getLadderConfig(), ActivityLadderConfigVO.class);
           activitySkuDetailVO.setActivityLadderConfigDTOList(activityLadderConfigVOS);
       }
       activitySkuDetailVO.setMarketingCostSharingRatio(activitySkuDetailEntity.getMarketingCostSharingRatio());
       return activitySkuDetailVO;
   }

    public static List<ActivitySkuDetailCommandParam> buildCreateParams(List<ActivitySkuDetailInput> skuDetailList, Long itemConfigId, Integer systemOrigin) {
        if (CollectionUtils.isEmpty(skuDetailList)) {
            return Collections.emptyList();
        }

        List<ActivitySkuDetailCommandParam> activitySkuDetailCommandParams = new ArrayList<>();
        for (ActivitySkuDetailInput activitySkuDetailInput : skuDetailList) {
            activitySkuDetailCommandParams.add(buildCreateParam(activitySkuDetailInput, itemConfigId, systemOrigin));
        }
        return activitySkuDetailCommandParams;
    }
}
