package net.xianmu.marketing.center.application.service.activity.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.xianmu.authentication.common.utils.AuthUserUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.inventory.client.productcost.dto.res.ProductCostQueryResp;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivityBasicInfoAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivityItemConfigAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivityScopeConfigAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivitySkuDetailAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.ActivityPageQueryInput;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.*;
import net.xianmu.marketing.center.application.service.activity.ActivityQueryService;
import net.xianmu.marketing.center.application.inbound.converter.activity.ActivityInputConverter;
import net.xianmu.marketing.center.application.inbound.converter.activity.ActivityResultConverter;
import net.xianmu.marketing.center.common.converter.PageInfoConverter;
import net.xianmu.marketing.center.common.enums.activity.ActivityAuditStatusEnum;
import net.xianmu.marketing.center.common.enums.activity.ActivityStatusEnum;
import net.xianmu.marketing.center.common.enums.activity.ActivityTypeEnum;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.BatchCheckInput;
import net.xianmu.marketing.center.common.enums.activity.ScopeTypeEnum;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.dto.ActivityItemConfigDTO;
import net.xianmu.marketing.center.domain.activity.dto.ActivityPageRespDTO;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeConfigDTO;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeQueryDTO;
import net.xianmu.marketing.center.domain.activity.entity.*;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityBasicPageQueryParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuPriceQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.*;
import net.xianmu.marketing.center.domain.activity.service.ActivityDomainService;
import net.xianmu.marketing.center.domain.activity.param.BatchCheckQueryParam;
import net.xianmu.marketing.center.domain.activity.service.ActivityItemConfigQueryDomainService;
import net.xianmu.marketing.center.domain.activity.service.ActivityScopeConfigQueryDomainService;
import net.xianmu.marketing.center.domain.cms.converter.CmsPageInfoConverter;
import net.xianmu.marketing.center.facade.admin.AdminQueryFacade;
import net.xianmu.marketing.center.facade.area.AreaQueryFacade;
import net.xianmu.marketing.center.facade.area.dto.AreaSimpleDTO;
import net.xianmu.marketing.center.facade.item.AreaSkuFacade;
import net.xianmu.marketing.center.facade.item.ProductCostQueryFacade;
import net.xianmu.marketing.center.facade.item.dto.AreaSkuDTO;
import net.xianmu.marketing.center.facade.item.dto.ProductCostQueryDto;
import net.xianmu.marketing.center.facade.item.input.AreaSkuQueryInput;
import net.xianmu.marketing.center.facade.wnc.WarehouseSkuAreaNoQueryFacade;
import net.xianmu.marketing.center.facade.wnc.dto.WarehouseBySkuAreaNoDTO;
import net.xianmu.marketing.center.facade.wnc.input.WarehouseBySkuAreaNoDataInput;
import org.springframework.stereotype.Service;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Slf4j
@Service
public class ActivityQueryServiceImpl implements ActivityQueryService {

    @Resource
    private ActivityDomainService activityDomainService;

    @Resource
    private ActivityBasicInfoQueryRepository activityBasicInfoQueryRepository;

    @Resource
    private ActivitySceneConfigQueryRepository sceneConfigQueryRepository;

    @Resource
    private ActivityScopeConfigQueryDomainService scopeConfigQueryDomainService;

    @Resource
    private ActivityItemConfigQueryDomainService itemConfigQueryDomainService;

    @Resource
    private ActivitySkuDetailQueryRepository skuDetailQueryRepository;

    @Resource
    private ActivitySkuScopeQueryRepository skuScopeQueryRepository;

    @Resource
    private AreaQueryFacade areaQueryFacade;

    @Resource
    private ActivitySkuPriceQueryRepository skuPriceQueryRepository;

    @Resource
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;

    @Resource
    private ProductCostQueryFacade productCostQueryFacade;

    @Resource
    private AreaSkuFacade areaSkuFacade;

    @Resource
    private AdminQueryFacade adminQueryFacade;


    @Override
    public RepeatActivityVO listRepeatActivity(BatchCheckInput input) {
        RepeatActivityVO repeatActivityVO = new RepeatActivityVO();
        if (!Objects.equals(input.getIsPermanent(), 1)) {
            if (input.getStartTime() == null || input.getEndTime() == null) {
                log.error("非永久的活动开始时间和结束时间不能为空,input:{}", input);
                return repeatActivityVO;
            }
        }
        BatchCheckQueryParam queryParam = ActivityInputConverter.toBatchCheckQueryParam(input);
        queryParam.setType(ActivityTypeEnum.SPECIAL_PRICE.getCode());
        List<RepeatActivitySkuEntity> entityList = activityDomainService.listRepeatActivity(queryParam);
        List<RepeatActivitySkuVO> repeatList = ActivityResultConverter.toRepeatActivitySkuVOList(entityList);
        repeatActivityVO.setRepeatList(repeatList);
        return repeatActivityVO;
    }

    @Override
    public PageInfo<ActivityPageRespVO> page(ActivityPageQueryInput pageQueryDTO) {
        ActivityBasicPageQueryParam basicQueryDTO = new ActivityBasicPageQueryParam();
        basicQueryDTO.setType(pageQueryDTO.getType());
        basicQueryDTO.setId(pageQueryDTO.getId());
        basicQueryDTO.setActivityStatus(pageQueryDTO.getActivityStatus());
        basicQueryDTO.setCreatorId(pageQueryDTO.getCreatorId());
        basicQueryDTO.setName(pageQueryDTO.getName());
        basicQueryDTO.setSku(pageQueryDTO.getSku());
        List<ActivityScopeQueryDTO> scopeList = Lists.newArrayList();
        if (pageQueryDTO.getMerchantPoolId() != null) {
            scopeList.add(new ActivityScopeQueryDTO(pageQueryDTO.getMerchantPoolId(),
                    ScopeTypeEnum.MERCHANT_POOL.getCode()));
        }
        if (pageQueryDTO.getLargeAreaNo() != null) {
            //需要查询大区和大区下运营城市的活动
            scopeList.add(new ActivityScopeQueryDTO(Long.valueOf(pageQueryDTO.getLargeAreaNo()),
                    ScopeTypeEnum.LARGE_AREA.getCode()));
            //查询大区下生效的运营城市
            List<AreaSimpleDTO> areaNos = areaQueryFacade.batchQueryEffectiveByLargeAreaNos(
                    Lists.newArrayList(pageQueryDTO.getLargeAreaNo()));
            areaNos.forEach(x -> {
                ActivityScopeQueryDTO queryDTO = new ActivityScopeQueryDTO(x.getAreaNo().longValue(),
                        ScopeTypeEnum.AREA.getCode());
                scopeList.add(queryDTO);
            });
        }
        basicQueryDTO.setScopeList(scopeList);
        PageInfo<ActivityPageRespDTO> pageInfo = PageInfoHelper.createPageInfo(
                pageQueryDTO.getPageIndex(), pageQueryDTO.getPageSize(), () -> {
                    List<ActivityPageRespDTO> activityBasicInfos = activityBasicInfoQueryRepository.listByQuery(
                            basicQueryDTO);
                    return activityBasicInfos;
                });

        if (CollectionUtil.isEmpty(pageInfo.getList())) {
            return PageInfoConverter.toPageResp(pageInfo, ActivityBasicInfoAssembler::toActivityBasicInfoVO);
        }

        pageInfo.getList().forEach(this::buildPageRespDTO);
        return PageInfoConverter.toPageResp(pageInfo, ActivityBasicInfoAssembler::toActivityBasicInfoVO);

    }



    /**
     * 构建补充列表页信息
     *
     * @param respDTO
     */
    private void buildPageRespDTO(ActivityPageRespDTO respDTO) {
        //获取活动范围
        ActivityScopeConfigDTO activityScopeConfigEntity = scopeConfigQueryDomainService
                .buildScopeConfig(respDTO.getBasicInfoId(), AuthUserUtils.getSystemOrigin().getType(), true);
        List<ActivityScopeEntity> scopes = activityScopeConfigEntity.getScopes();

        respDTO.setTotalScope(scopes.size());
        Integer scopeType = activityScopeConfigEntity.getScopeType();
        respDTO.setScopeType(scopeType);
        respDTO.setScopeNameList(scopes.stream().map(ActivityScopeEntity::getScopeName).collect(Collectors.toList()));

        //判断活动状态
        ActivityStatusEnum activityStatusEnum = checkActivityStatus(respDTO.getDelFlag(),
                respDTO.getStatus(), respDTO.getIsPermanent(), respDTO.getStartTime(), respDTO.getEndTime()
                );
        respDTO.setActivityStatus(activityStatusEnum == null ? null : activityStatusEnum.getCode());
    }

    private String getCreatorName(ActivityPageRespDTO respDTO){
        if (respDTO.getCreatorId() == 0) {
            respDTO.setCreatorName("系统");
        } else {
            //查询创建人
        }
        return null;
    }

    @Override
    public List<LargeAreaSkuPriceVO> listSkuPrice(Long basicInfoId, String sku) {
        //校验sku是否在活动中
        List<ActivitySkuDetailEntity> skuDetailList = skuDetailQueryRepository.listByBasicInfoIds(Lists.newArrayList(basicInfoId), sku);
        if (CollectionUtil.isEmpty(skuDetailList)) {
            throw new BizException("当前活动中不存在此sku");
        }

        List<ActivityScopeConfigEntity> scopeConfigs = skuScopeQueryRepository.selectByInfoId(basicInfoId);
        if (CollectionUtil.isEmpty(scopeConfigs)) {
            throw new BizException("当前活动范围缺失");
        }

        Integer scopeType = scopeConfigs.get(0).getScopeType();
        if (Objects.equals(scopeType, ScopeTypeEnum.MERCHANT_POOL.getCode())) {
            log.warn("活动范围为人群包，无法获取价格信息");
            return Collections.emptyList();
        }

        List<LargeAreaSkuPriceVO> list = Lists.newArrayList();
        List<Integer> scopeIds = scopeConfigs.stream().map(x -> x.getScopeId().intValue())
                .collect(Collectors.toList());
        List<AreaSimpleDTO> areaInfoDTOS = Lists.newArrayList();
        if (Objects.equals(scopeType, ScopeTypeEnum.AREA.getCode())) {
            areaInfoDTOS = areaQueryFacade.batchQueryEffectiveByAreaNos(scopeIds);
        }

        //大区也先转换成运营城市去处理，最终再匹配城市：大区对应关系
        if (Objects.equals(scopeType, ScopeTypeEnum.LARGE_AREA.getCode())) {
            areaInfoDTOS = areaQueryFacade.batchQueryEffectiveByLargeAreaNos(scopeIds);
        }
        if (CollectionUtil.isEmpty(areaInfoDTOS)) {
            return list;
        }

        Map<Integer, AreaSimpleDTO> areaNoMap = areaInfoDTOS.stream()
                .collect(Collectors.toMap(x -> x.getAreaNo(), Function.identity(), (p1, p2) -> p2));
        Set<Integer> areaNos = areaNoMap.keySet();

        //查询仓库信息
        WarehouseBySkuAreaNoDataInput input = new WarehouseBySkuAreaNoDataInput();
        input.setSku(sku);
        input.setAreaNoList(new ArrayList<>(areaNos));
        List<WarehouseBySkuAreaNoDTO> warehouseBySkuAreaNoDTOS = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo(Collections.singletonList(input));
        Map<Integer, Integer> areaWarehouseMap = warehouseBySkuAreaNoDTOS.stream().collect(Collectors.toMap(x ->
                x.getAreaNo(), x -> x.getWarehouseNo(), (p1, p2) -> p2));

        // 获取成本集合
        List<ProductCostQueryDto> reqList = new ArrayList<>();
        for (WarehouseBySkuAreaNoDTO warehouseBySkuAreaNoDTO : warehouseBySkuAreaNoDTOS) {
            ProductCostQueryDto dto = new ProductCostQueryDto();
            dto.setWarehouseNo(warehouseBySkuAreaNoDTO.getWarehouseNo());
            dto.setSku(warehouseBySkuAreaNoDTO.getSku());
            reqList.add(dto);
        }
        Map<String, ProductCostQueryResp> costQueryRespMap = productCostQueryFacade.selectMapBySkuAndWarehouseNos(reqList);

        //根据运营城市去获取成本价、原价信息
        ActivitySkuPriceQueryParam param = new ActivitySkuPriceQueryParam();
        param.setBasicInfoId(basicInfoId);
        param.setSku(sku);
        List<ActivitySkuPriceEntity> activitySkuPrices = skuPriceQueryRepository.selectByCondition(param);

        AreaSkuQueryInput skuQueryInput = new AreaSkuQueryInput();
        skuQueryInput.setSku(sku);
        skuQueryInput.setAreaNos(new ArrayList<>(areaNos));
        List<AreaSkuDTO> areaSkus = areaSkuFacade.querySkuAreaPrice(Collections.singletonList(skuQueryInput));
        Map<Integer, BigDecimal> areaSkuPriceMap = areaSkus.stream()
                .collect(Collectors.toMap(x -> x.getAreaNo(), x -> x.getPrice(), (p1, p2) -> p2));

        Map<Integer, List<ActivityLadderPriceVO>> activityPriceMap = activitySkuPrices.stream()
                .collect(Collectors.toMap(ActivitySkuPriceEntity::getAreaNo, x -> JSON.parseArray(x.getLadderPrice(), ActivityLadderPriceVO.class)));

        List<ActivitySkuPriceVO> skuPriceList = Lists.newArrayList();
        Map<Integer, BigDecimal> costPriceMap = new HashMap<>(16);
        for (Integer areaNo : areaNos) {
            Integer warehouseNo = areaWarehouseMap.get(areaNo);
            BigDecimal salePrice = areaSkuPriceMap.get(areaNo);
            if (warehouseNo == null || salePrice == null) {
                continue;
            }

            ActivitySkuPriceVO skuPriceDTO = new ActivitySkuPriceVO();
            AreaSimpleDTO areaInfoDTO = areaNoMap.get(areaNo);
            skuPriceDTO.setLargeAreaName(areaInfoDTO.getLargeAreaName());
            skuPriceDTO.setAreaName(areaInfoDTO.getAreaName());
            List<ActivityLadderPriceVO> ladderPriceDTOS = activityPriceMap.get(areaNo);
            skuPriceDTO.setActivityLadderPriceList(ladderPriceDTOS);
            skuPriceDTO.setSalePrice(salePrice);
            BigDecimal costPrice = costPriceMap.get(warehouseNo);
            if (costPrice != null) {
                skuPriceDTO.setCostPrice(costPrice);
            } else {
                ProductCostQueryResp productCostQueryResp = costQueryRespMap.get(warehouseNo + "|" + sku);
                if (productCostQueryResp != null) {
                    costPrice = productCostQueryResp.getCurrentCost();
                    costPriceMap.put(warehouseNo, costPrice);
                    skuPriceDTO.setCostPrice(costPrice);
                }
            }
            skuPriceList.add(skuPriceDTO);
        }
        Map<String, List<ActivitySkuPriceVO>> listMap = skuPriceList.stream()
                .collect(Collectors.groupingBy(ActivitySkuPriceVO::getLargeAreaName));
        for (Map.Entry<String, List<ActivitySkuPriceVO>> entry : listMap.entrySet()) {
            LargeAreaSkuPriceVO largeAreaSkuPriceDTO = new LargeAreaSkuPriceVO();
            largeAreaSkuPriceDTO.setLargeAreaName(entry.getKey());
            largeAreaSkuPriceDTO.setAreaPriceList(entry.getValue());
            list.add(largeAreaSkuPriceDTO);
        }
        return list;
    }

    @Override
    public ActivityDetailVO getDetail(Long basicInfoId, Integer systemOrigin) {
        ActivityDetailVO activityDetailVO = new ActivityDetailVO();

        //校验活动id
        ActivityBasicInfoEntity basicInfoEntity = activityBasicInfoQueryRepository.selectById(basicInfoId);
        if (basicInfoEntity == null) {
            throw new BizException("活动不存在");
        }

        //构建基础信息
        ActivityBasicInfoVO basicInfoVO = ActivityBasicInfoAssembler.toActivityBasicInfoVO(basicInfoEntity);
        ActivitySceneConfigEntity sceneConfig = sceneConfigQueryRepository.selectByBasicInfoId(basicInfoId);
        basicInfoVO.setPlace(sceneConfig.getPlace());
        basicInfoVO.setPlatform(sceneConfig.getPlatform());

        ActivityStatusEnum activityStatusEnum = this.checkActivityStatus(basicInfoVO.getDelFlag(), basicInfoVO.getStatus(), basicInfoVO.getIsPermanent(), basicInfoVO.getStartTime(), basicInfoVO.getEndTime());
        basicInfoVO.setActivityStatus(activityStatusEnum.getCode());
        activityDetailVO.setBasicInfoDTO(basicInfoVO);

        //构建活动范围
        ActivityScopeConfigDTO activityScopeConfig = scopeConfigQueryDomainService.buildScopeConfig(basicInfoId, systemOrigin, true);
        ActivityScopeConfigVO activityScopeConfigVO = ActivityScopeConfigAssembler.toActivityScopeConfigVO2(activityScopeConfig);
        activityDetailVO.setScopeConfigDTO(activityScopeConfigVO);

        //构建商品信息
        ActivityItemConfigDTO activityItemConfigEntity = itemConfigQueryDomainService.buildItemConfig(basicInfoId, systemOrigin, true);
        ActivityItemConfigVO activityItemConfigVO = ActivityItemConfigAssembler.toActivityItemConfigVO2(activityItemConfigEntity);
        List<ActivitySkuDetailEntity> skuDetailList = activityItemConfigEntity.getSkuDetailList();
        List<ActivitySkuDetailVO> activitySkuDetailVOS = ActivitySkuDetailAssembler.toActivitySkuDetailVOList(skuDetailList);
        activityItemConfigVO.setSkuDetailList(activitySkuDetailVOS);
        activityDetailVO.setItemConfigDTO(activityItemConfigVO);
        return activityDetailVO;
    }

    /**
     * 判断活动状态
     * @return
     */
    public ActivityStatusEnum checkActivityStatus(Integer delFlag, Integer status, Integer isPermanent, LocalDateTime startTime, LocalDateTime endTime) {
        if (Objects.equals(delFlag, CommonStatus.YES.getCode())) {
            return ActivityStatusEnum.DELETE;
        }

        //新增审核状态
        if (Objects.equals(status, ActivityStatusEnum.PENDING_REVIEW.getCode())) {
            return ActivityStatusEnum.PENDING_REVIEW;
        } else if (Objects.equals(status, ActivityStatusEnum.REJECTED.getCode())) {
            return ActivityStatusEnum.REJECTED;
        }

        //永久
        if (CommonStatus.YES.getCode().equals(isPermanent)) {
            if (CommonStatus.YES.getCode().equals(status)) {
                return ActivityStatusEnum.EFFECTING;
            } else {
                return ActivityStatusEnum.NOT_VALID;
            }
        }
        //固定时间
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(startTime)) {
            return ActivityStatusEnum.NOT_VALID;
        }

        if (now.isAfter(startTime) && now.isBefore(endTime)) {
            if (CommonStatus.YES.getCode().equals(status)) {
                return ActivityStatusEnum.EFFECTING;
            } else {
                return ActivityStatusEnum.NOT_VALID;
            }
        }

        if (now.isAfter(endTime)) {
            return ActivityStatusEnum.FINISHED;
        }
        return null;
    }
}
