package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;
import net.xianmu.marketing.center.common.enums.activity.PlatformEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @author: <EMAIL>
 * @create: 2022/12/1
 */
@Data
public class ActivityBasicInfoInput implements Serializable {

    private Long basicInfoId;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    private String name;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否永久，0 否，1 是
     */
    private Integer isPermanent;

    /**
     * 活动状态，0 暂停，1 开启（审核通过），-1审核中（待审核），100审核拒绝
     */
    private Integer status;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 是否需要预热，0 否，1 是
     */
    private Integer needPre;

    /**
     * 预热开始时间
     */
    private LocalDateTime preStartTime;

    /**
     * 预热结束时间
     */
    private LocalDateTime preEndTime;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    @NotNull(message = "请选择活动类型")
    private Integer type;

    /**
     * 活动目的(标签)，0 滞销促销，1 临保清仓，2 新品推广，3 用户召回，4 潜力品推广
     */
    @NotNull(message = "请选择活动目的")
    private Integer tag;

    /**
     * 活动备注
     */
    private String remark;

    /**
     * 生效平台，0 商城，1 直播，2 其他
     * @see PlatformEnum
     */
    private Integer platform;

    /**
     * 投放区域，枚举值待补充
     */
    private Integer place;

    /**
     * 活动限购类型，0 不限购，1 每人，2 每人每天
     */
    private Integer limitType;

    /**
     * 活动限购数量
     */
    private Integer limitNum;

    /**
     * 活动特殊规则json
     */
    private String ruleDetail;

    /**
     * 创建人id  auth_user.biz_user_id
     */
    private Integer creatorId;

    /**
     * 最后一次修改人id
     */
    private Integer updaterId;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 归属人  平台默认为null 供应商supplier_id
     */
    private Long ownerId;

    /**
     * 创建人系统来源  0-srm, 2-admin
     */
    private Integer systemOrigin;

    /**
     * 审核状态 0-待审核  1-审核通过  2-审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核备注
     */
    private String auditRemark;
}
