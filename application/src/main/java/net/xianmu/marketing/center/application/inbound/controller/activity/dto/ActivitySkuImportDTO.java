package net.xianmu.marketing.center.application.inbound.controller.activity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/11 18:31
 * @PackageName:net.xianmu.marketing.center.application.inbound.controller.activity.dto
 * @ClassName: ActivitySkuImportDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ActivitySkuImportDTO {

    @ColumnWidth(50)
    @ExcelProperty(value = "sku", index = 0)
    private String sku;


    /**
     * 活动价(限定 指定价形式)
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "阶梯数量", index = 1)
    private Integer unit;

    /**
     * 活动价(限定 指定价形式)
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "活动价（默认指定价）", index = 2)
    private BigDecimal amount;

    /**
     * 活动库存
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "活动库存", index = 3)
    private Integer actualQuantity;

    /**
     * 限购件数
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "限购件数（为空则不限购）", index = 4)
    private Integer limitQuantity;
}
