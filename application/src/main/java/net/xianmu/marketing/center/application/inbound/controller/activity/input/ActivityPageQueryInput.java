package net.xianmu.marketing.center.application.inbound.controller.activity.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

/**
 * @author: <EMAIL>
 * @create: 2022/12/5
 */
@Data
public class ActivityPageQueryInput extends BasePageInput implements Serializable {

    /**
     * 创建者id
     */
    private Integer creatorId;

    /**
     * 活动id
     */
    private Long id;

    /**
     * 活动大区id
     */
    private Integer largeAreaNo;

    /**
     * 人群包id
     */
    private Long merchantPoolId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动状态，0 未生效，1 已生效，2 已失效，3已删除
     */
    private Integer activityStatus;

    /**
     * 审核状态 0-待审核  1-审核通过  2-审核拒绝
     */
    private Integer auditStatus;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * sku和活动大区只能2选1查询，否则会慢sql
     */
    private String sku;

    /**
     * 归属人 平台默认为null 供应商supplier_id
     */
    private Long ownerId;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
}
