package net.xianmu.marketing.center.application.inbound.controller.activity.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2022/12/8
 */
@Data
public class ActivityPageRespVO implements Serializable {

    private Long basicInfoId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否永久，0 否，1 是
     */
    private Integer isPermanent;

    /**
     * 活动状态，-1 待审核, 0 未生效，1 已生效，2 已失效，3已删除，100 审核拒绝
     */
    private Integer activityStatus;

    /**
     * 开关状态，0 暂停，1 开启
     */
    private Integer status;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * 活动目的(标签)，0 滞销促销，1 临保清仓，2 新品推广，3 用户召回，4 潜力品推广
     */
    private Integer tag;

    /**
     * 活动备注
     */
    private String remark;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 最后一次修改人id
     */
    private Integer updaterId;

    /**
     * 是否已被删除，0 否，1 是
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 活动范围类型
     */
    private Integer scopeType;

    /**
     * 范围总数
     */
    private Integer totalScope;

    /**
     * 活动范围
     */
    private List<ActivityScopeConfigVO> scopeConfigs;

    /**
     * 活动范围名称
     */
    private List<String> scopeNameList;

    /**
     * 当前活动商品数量
     */
    private List<Integer> skuNum;

}
