package net.xianmu.marketing.center.application.service.activity;

import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.*;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.*;

import java.util.List;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
public interface ActivityCommandService {

    /**
     * 批量删除活动商品
     *
     * @param input
     * @return
     */
    Boolean batchDeleteItem(BatchDeleteInput input);

    /**
     * 创建活动
     * @param input
     * @return
     */
    RepeatActivityVO addBasicInfo(ActivityInsertInput input);

    /**
     * 修改活动基础信息
     * @param input
     * @return
     */
    Boolean updateBasicInfo(ActivityBasicInfoUpdateInput input);

    /**
     * 删除活动
     * @param basicInfoId
     * @param updaterId
     * @return
     */
    Boolean delete(Long basicInfoId, Long updaterId);

    /**
     * @description: 开启/关闭 自动定价
     * @param input
     * @return: java.lang.Boolean
     **/
    Boolean automaticPrice(ActivityItemConfigInput input);

    /**
     * excel批量新增活动sku
     * @param input
     * @return
     */
    ActivitySkuBatchVO batchAddSku(ActivityBatchSkuInput input);

    /**
     * 开启关闭活动
     * @param basicInfoId
     * @param status
     * @return
     */
    Boolean openDown(Long basicInfoId, Integer status, Long updaterId);

    /**
     * 删除活动商品
     * @param basicInfoId
     * @param sku
     * @return
     */
    Boolean deleteItem(Long basicInfoId, String sku, Long updaterId);

    /**
     * 新增、修改活动商品
     * @param input
     * @return
     */
    Long updateItem(ActivityItemConfigInput input);

    /**
     * 删除活动范围
     * @param input
     * @return
     */
    Boolean deleteScopeConfig(ActivityScopeConfigInput input);

    /**
     * 新增活动范围
     * @param input
     * @return
     */
    Boolean addScopeConfig(ActivityScopeConfigInput input);
}
