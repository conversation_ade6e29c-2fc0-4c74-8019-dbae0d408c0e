package net.xianmu.marketing.center.application.service.activity.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.common.utils.AuthUserUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivityBasicInfoAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivityItemConfigAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivityScopeConfigAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.assembler.ActivitySkuDetailAssembler;
import net.xianmu.marketing.center.application.inbound.controller.activity.dto.ActivitySkuImportDTO;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.*;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.*;
import net.xianmu.marketing.center.application.inbound.converter.activity.ActivityInputConverter;
import net.xianmu.marketing.center.application.service.activity.ActivityCommandService;
import net.xianmu.marketing.center.common.enums.activity.*;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.dto.ActivityItemConfigDTO;
import net.xianmu.marketing.center.domain.activity.dto.ActivityScopeConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.*;
import net.xianmu.marketing.center.domain.activity.param.BatchCheckQueryParam;
import net.xianmu.marketing.center.domain.activity.param.BatchDeleteQueryParam;
import net.xianmu.marketing.center.domain.activity.param.ScopeQueryParam;
import net.xianmu.marketing.center.domain.activity.param.command.*;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityScopeConfigQueryParam;
import net.xianmu.marketing.center.domain.activity.param.query.ActivitySkuDetailQueryParam;
import net.xianmu.marketing.center.domain.activity.repository.ActivityBasicInfoCommandRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivityBasicInfoQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivityItemConfigQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuDetailQueryRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuPriceCommandRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuScopeCommandRepository;
import net.xianmu.marketing.center.domain.activity.repository.ActivitySkuScopeQueryRepository;
import net.xianmu.marketing.center.domain.activity.service.*;
import net.xianmu.marketing.center.facade.area.AreaQueryFacade;
import net.xianmu.marketing.center.facade.area.dto.AreaSimpleDTO;
import net.xianmu.marketing.center.facade.item.ItemQueryFacade;
import net.xianmu.marketing.center.facade.item.dto.SkuInfoDetailDTO;
import net.xianmu.marketing.center.facade.item.input.BatchSkuInput;
import net.xianmu.marketing.center.facade.wnc.WncDeliveryFenceQueryFacade;
import net.xianmu.marketing.center.facade.wnc.dto.AreaWarehouseNoSkuDTO;
import net.xianmu.marketing.center.facade.wnc.input.SkuWarehouseNoQueryAreaInput;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@Transactional(rollbackFor = Exception.class)
@Slf4j
@Service
public class ActivityCommandServiceImpl implements ActivityCommandService {

    @Resource
    private ActivityDomainService activityDomainService;

    @Resource
    private ActivityBasicInfoCommandDomainService basicInfoCommandDomainService;

    @Resource
    private ActivitySceneConfigCommandDomainService  sceneConfigCommandDomainService;

    @Resource
    private ActivityScopeConfigCommandDomainService scopeConfigCommandDomainService;

    @Resource
    private ActivityScopeConfigQueryDomainService scopeConfigQueryDomainService;

    @Resource
    private ActivityItemConfigQueryDomainService itemConfigQueryDomainService;

    @Resource
    private ActivityItemConfigCommandDomainService itemConfigCommandDomainService;

    @Resource
    private ActivitySkuDetailCommandDomainService skuDetailCommandDomainService;

    @Resource
    private ActivityBasicInfoQueryRepository activityBasicInfoQueryRepository;

    @Resource
    private ActivityBasicInfoCommandRepository activityBasicInfoCommandRepository;

    @Resource
    private ActivitySkuDetailQueryRepository skuDetailQueryRepository;

    @Resource
    private ActivitySkuScopeQueryRepository activitySkuScopeQueryRepository;

    @Resource
    private ActivitySkuScopeCommandRepository activitySkuScopeCommandRepository;

    @Resource
    private ActivitySkuPriceCommandRepository activitySkuPriceCommandRepository;

    @Resource
    private ActivitySkuPriceCommandDomainService skuPriceCommandDomainService;

    @Resource
    private ActivityItemConfigQueryRepository itemConfigQueryRepository;

    @Resource
    private ItemQueryFacade itemQueryFacade;

    @Resource
    private AreaQueryFacade areaQueryFacade;

    @Resource
    private WncDeliveryFenceQueryFacade wncDeliveryFenceQueryFacade;

    @Override
    public Boolean batchDeleteItem(BatchDeleteInput input) {
        BatchDeleteQueryParam param = ActivityInputConverter.toBatchDeleteQueryParam(input);
        return activityDomainService.batchDeleteItem(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(key = "ActivityCommandService.addBasicInfo:{input.creatorId}")
    public RepeatActivityVO addBasicInfo(ActivityInsertInput input) {
        if (input.getCreatorId() == null) {
            throw new BizException("创建者ID不能为空");
        }

        //校验信息判空
        RepeatActivityVO repeatActivityVO  = this.check(input);
        if (repeatActivityVO != null) {
            return repeatActivityVO;
        }
        input.getItemConfigDTO().setGoodSelectWay(GoodSelectWayEnum.SKU.getCode());

        //构建并保存活动基础信息
        ActivityBasicInfoInput basicInfoDTO = input.getBasicInfoDTO();
        ActivityBasicInfoCommandParam param = ActivityBasicInfoAssembler.buildCreateParam(basicInfoDTO);
        param.setCreatorId(input.getCreatorId().intValue());
        param.setStatus(CommonStatus.YES.getCode());

        //假如是供应商创建 赋予默认值
        if (Objects.equals(param.getSystemOrigin(), SystemOriginEnum.SRM.getType())) {
            param.setAuditStatus(ActivityAuditStatusEnum.PENDING_REVIEW.getCode());
            param.setStatus(ActivityStatusEnum.PENDING_REVIEW.getCode());
            param.setTag(ActivityTagEnum.SLOW_SALE_PROMOTION.getCode());
        }
        ActivityBasicInfoEntity basicInfoEntity = basicInfoCommandDomainService.insert(param);
        if (basicInfoEntity.getId() == null) {
            throw new BizException("创建活动基础信息失败");
        }

        //保存活动场景信息
        ActivitySceneConfigCommandParam sceneConfigParam = new ActivitySceneConfigCommandParam();
        sceneConfigParam.setBasicInfoId(basicInfoEntity.getId());
        sceneConfigParam.setPlatform(basicInfoDTO.getPlatform());
        if (sceneConfigParam.getPlatform() == null) {
            sceneConfigParam.setPlatform(PlatformEnum.MALL.getCode());
        }
        sceneConfigCommandDomainService.insert(sceneConfigParam);

        //构建并保存活动范围信息
        ActivityScopeConfigInput scopeConfigDTO = input.getScopeConfigDTO();
        List<ActivityScopeConfigCommandParam> scopeConfigParams = ActivityScopeConfigAssembler.toActivityScopeConfigCommandParamList(scopeConfigDTO, basicInfoEntity.getId(), input.getCreatorId());

        //假如是供应商创建活动需要将仓维度进行转换成运营大区维度
        ActivityItemConfigInput itemConfigDTO = input.getItemConfigDTO();
        supplierScopeConfig(input, param, basicInfoEntity, scopeConfigDTO, scopeConfigParams, itemConfigDTO);
        List<ActivityScopeConfigEntity> scopeConfigEntities = scopeConfigCommandDomainService.insertBatch(scopeConfigParams);

        //构建并保存sku相关信息
        ActivityItemConfigCommandParam itemConfigCommandParam = ActivityItemConfigAssembler.buildCreateParam(itemConfigDTO);
        itemConfigCommandParam.setBasicInfoId(basicInfoEntity.getId());
        ActivityItemConfigEntity itemConfigEntity = itemConfigCommandDomainService.insert(itemConfigCommandParam);

        //判断活动商品配置类型
        List<ActivitySkuDetailInput> skuDetailList = itemConfigDTO.getSkuDetailList();
        Map<String, Long> skuCount = skuDetailList.stream().map(x -> x.getSku())
                .collect(Collectors.groupingBy(x -> x, Collectors.counting()));
        List<String> repeatSkus = skuCount.entrySet().stream().filter(x -> x.getValue() > 1)
                .map(x -> x.getKey()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(repeatSkus)) {
            String skuStr = repeatSkus.stream().collect(Collectors.joining(","));
            throw new BizException("活动创建失败，存在重复的活动商品:" + skuStr);
        }

        List<ActivitySkuDetailCommandParam> skuDetailParams = null;
        List<ActivitySkuDetailEntity> activitySkuDetailEntities = null;
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), itemConfigDTO.getGoodSelectWay())) {
            skuDetailParams = ActivitySkuDetailAssembler.buildCreateParams(skuDetailList, itemConfigEntity.getId(), param.getSystemOrigin());
            activitySkuDetailEntities = skuDetailCommandDomainService.insertBatch(skuDetailParams);
        } else {
            //其他类型
        }

        //异步计算活动价并保存&&供应商创建活动需要发送审批流
        activityDomainService.syncSendFeiShuApproval(basicInfoEntity, scopeConfigEntities, activitySkuDetailEntities, itemConfigEntity, Boolean.TRUE);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(key = "ActivityCommandService.updateBasicInfo:{input.updaterId}")
    public Boolean updateBasicInfo(ActivityBasicInfoUpdateInput input) {
        //校验活动id
        ActivityBasicInfoEntity activityBasicInfoEntity = activityBasicInfoQueryRepository.selectById(input.getBasicInfoId());
        if (activityBasicInfoEntity == null) {
            throw new BizException("活动不存在");
        }

        if (input.getType() != null && !Objects.equals(input.getType(), activityBasicInfoEntity.getType())) {
            throw new BizException("活动类型不支持修改");
        }

        //改基础信息，需要校验时间重复问题
        //构建活动范围
        ActivityScopeConfigDTO activityScopeConfig = scopeConfigQueryDomainService.buildScopeConfig(input.getBasicInfoId(), input.getSystemOrigin(), false);
        if (activityBasicInfoEntity == null) {
            throw new BizException("活动数据异常，活动范围不存在！");
        }
        input.setScopeType(activityScopeConfig.getScopeType());
        this.updateCheck(input, activityScopeConfig);
        ActivityBasicInfoCommandParam param = ActivityBasicInfoAssembler.buildUpdateParam(input);
        basicInfoCommandDomainService.update(param);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long basicInfoId, Long updaterId) {
        //校验活动id
        ActivityBasicInfoEntity basicInfo = activityBasicInfoQueryRepository.selectById(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }

        //生效中不能删除
        LocalDateTime now = LocalDateTime.now();
        boolean flag = basicInfo.getIsPermanent() == 1 || (now.isAfter(basicInfo.getStartTime()) && now.isBefore(basicInfo.getEndTime()));

        //在活动时间范围内，且开启状态
        if (flag && basicInfo.getStatus() == 1) {
            throw new BizException("活动生效中，不能关闭");
        }

        //临保活动不支持创建、修改
        if (Objects.equals(basicInfo.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }

        //软删除
        ActivityBasicInfoCommandParam updateBasicInfo = new ActivityBasicInfoCommandParam();
        updateBasicInfo.setId(basicInfoId);
        updateBasicInfo.setUpdaterId(updaterId.intValue());
        updateBasicInfo.setDelFlag(CommonStatus.YES.getCode());
        basicInfoCommandDomainService.update(updateBasicInfo);

        //要将活动商品、活动范围也软删除;需要，因为会单个删除商品或者范围
        scopeConfigCommandDomainService.updateDelFlag(basicInfoId, updaterId);

        sceneConfigCommandDomainService.updateDelFlag(basicInfoId);

        ActivityItemConfigEntity itemConfigEntity = itemConfigQueryRepository.selectByInfoId(basicInfoId);
        if (itemConfigEntity != null) {
            ActivityItemConfigCommandParam itemConfig = new ActivityItemConfigCommandParam();
            itemConfig.setBasicInfoId(basicInfoId);
            itemConfig.setDelFlag(CommonStatus.YES.getCode());
            itemConfig.setUpdaterId(updaterId.intValue());
            itemConfigCommandDomainService.updateDelFlag(itemConfig);
            skuDetailCommandDomainService.updateDelFlag(itemConfigEntity.getId(), null);
        }

        //活动价格直接物理删除
        skuPriceCommandDomainService.deleteByInfoId(basicInfoId);
        log.info("营销活动:{}删除成功,操作人id:{}", basicInfoId, updaterId);
        return Boolean.TRUE;
    }

    @Override
    @XmLock(key = "ActivityCommandService.automaticPrice:{input.id}")
    public Boolean automaticPrice(ActivityItemConfigInput input) {
        ActivitySkuDetailEntity activitySkuDetail = skuDetailQueryRepository.selectById(input.getId());
        if (Objects.isNull(activitySkuDetail)) {
            throw new BizException("当前数据不存在！");
        }

        activitySkuDetail.setAutoPrice(input.getAutoPrice());
        ActivitySkuDetailCommandParam param = new ActivitySkuDetailCommandParam();
        param.setId(input.getId());
        param.setAutoPrice(input.getAutoPrice());
        int update = skuDetailCommandDomainService.update(param);
        return update > 0;
    }

    @Override
    public ActivitySkuBatchVO batchAddSku(ActivityBatchSkuInput input) {
        //解析文件数据
        InputStream inputStream = OssGetUtil.getInputStream(input.getKey());
        List<ActivitySkuImportDTO> activitySkuImportDTOS;
        try {
            activitySkuImportDTOS = EasyExcel.read(inputStream,
                    ActivitySkuImportDTO.class, null).doReadAllSync();
        } catch (Exception e) {
            throw new BizException("上传活动商品模版解析异常！");
        }

        if (CollectionUtil.isEmpty(activitySkuImportDTOS)) {
            throw new BizException("上传活动商品模版数据为空！");
        }

        ActivitySkuBatchVO activitySkuBatchVO = new ActivitySkuBatchVO();
        List<ActivitySkuDetailVO> skuDetailDTOList = Lists.newArrayList();
        List<String> failedSkus = Lists.newArrayList();
        List<String> skus = activitySkuImportDTOS.stream().filter(x -> !StringUtils.isEmpty(x.getSku()))
                .map(x -> x.getSku()).distinct().collect(Collectors.toList());
        BatchSkuInput batchSkuInput = new BatchSkuInput();
        batchSkuInput.setSkus(skus);
        List<SkuInfoDetailDTO> skuBaseInfoDTOS = itemQueryFacade.listBySkusNew(batchSkuInput);
        Map<String, SkuInfoDetailDTO> skuBaseInfoDTOMap = skuBaseInfoDTOS.stream()
                .collect(Collectors.toMap(x -> x.getSku(), Function.identity()));

        Map<String, List<ActivitySkuImportDTO>> collect = activitySkuImportDTOS.stream().collect(Collectors.groupingBy(ActivitySkuImportDTO::getSku));
        collect.forEach((sku, dtoList) -> {
            SkuInfoDetailDTO skuBaseInfoDTO = skuBaseInfoDTOMap.get(sku);
            if (skuBaseInfoDTO == null) {
                log.warn("【营销活动】未获取到sku信息,sku:{}", sku);
                failedSkus.add(sku);
                return;
            }

            ActivitySkuDetailVO skuDetailDTO = new ActivitySkuDetailVO();
            skuDetailDTO.setSku(sku);
            skuDetailDTO.setSkuName(skuBaseInfoDTO.getPdName());
            skuDetailDTO.setWeight(skuBaseInfoDTO.getWeight());
            skuDetailDTO.setUnit(skuBaseInfoDTO.getUnit());
            skuDetailDTO.setLogo(skuBaseInfoDTO.getPicturePath());

            List<ActivityLadderConfigVO> ladderConfigDTOS = new ArrayList<>();
            Integer actualQuantity = null;
            Integer limitQuantity = null;
            for (ActivitySkuImportDTO activitySkuImportDTO : dtoList) {
                ActivityLadderConfigVO ladderConfigDTO = new ActivityLadderConfigVO();
                // 默认阶梯配置
                ladderConfigDTO.setUnit(activitySkuImportDTO.getUnit());
                ladderConfigDTO.setRoundingMode(0);
                ladderConfigDTO.setAdjustType(AdjustTypeEnum.FIXED_PRICE.getCode());
                ladderConfigDTO.setAmount(activitySkuImportDTO.getAmount());
                ladderConfigDTOS.add(ladderConfigDTO);
                // 库存和限购数量只取第一个
                actualQuantity = actualQuantity == null ? activitySkuImportDTO.getActualQuantity() : actualQuantity;
                limitQuantity = limitQuantity == null ? activitySkuImportDTO.getLimitQuantity() : limitQuantity;
            }
            skuDetailDTO.setActivityLadderConfigDTOList(ladderConfigDTOS);
            //默认为正常定价、指定价形式、不限购
            if (limitQuantity == null || limitQuantity.compareTo(0) <= 0) {
                skuDetailDTO.setAccountLimit(AccountLimitEnum.NOT_LIMIT.getCode());
                skuDetailDTO.setLimitQuantity(0);
            } else {
                skuDetailDTO.setAccountLimit(AccountLimitEnum.NUMBER_LIMIT.getCode());
                skuDetailDTO.setLimitQuantity(limitQuantity);
            }
            skuDetailDTO.setActualQuantity(Optional.ofNullable(actualQuantity).orElse(100000));
            skuDetailDTOList.add(skuDetailDTO);
        });
        activitySkuBatchVO.setFailedSkus(failedSkus);
        activitySkuBatchVO.setSkuDetailDTOList(skuDetailDTOList);
        return activitySkuBatchVO;
    }

    @Override
    public Boolean openDown(Long basicInfoId, Integer status, Long updaterId) {
        ActivityBasicInfoEntity basicInfo = activityBasicInfoQueryRepository.selectById(basicInfoId);
        if (basicInfo == null) {
            throw new BizException("活动不存在");
        }
        Integer updateIdInt = updaterId == null ? 0 : updaterId.intValue();
        ActivityBasicInfoCommandParam updateBasicInfo = new ActivityBasicInfoCommandParam();
        updateBasicInfo.setId(basicInfoId);
        updateBasicInfo.setUpdaterId(updateIdInt);
        updateBasicInfo.setStatus(status);
        activityBasicInfoCommandRepository.updateSelectiveById(updateBasicInfo);
        log.info("营销活动:{}变更开关状态:{}成功,操作人id:{}, 操作人来源:{}", basicInfoId, status, updateIdInt, AuthUserUtils.getSystemOrigin());
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteItem(Long basicInfoId, String sku, Long updaterId) {
        //校验活动id
        ActivityBasicInfoEntity activityBasicInfoEntity = activityBasicInfoQueryRepository.selectById(basicInfoId);
        if (activityBasicInfoEntity == null) {
            throw new BizException("活动不存在");
        }

        //临保活动不支持创建、修改
        if (Objects.equals(activityBasicInfoEntity.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }

        ActivityItemConfigEntity itemConfig = itemConfigQueryRepository.selectByInfoId(basicInfoId);
        Long itemConfigId = itemConfig.getId();
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), itemConfig.getGoodSelectWay())) {
            int total = skuDetailQueryRepository.countByItemConfig(itemConfigId);
            if (total <= 1) {
                throw new BizException("不支持删除，活动sku至少保留一条");
            }
            int count = skuDetailCommandDomainService.updateDelFlag(itemConfigId, sku);
            if (count < 1) {
                throw new BizException("活动sku:" + sku + "不存在");
            }
            //需要联动删除价格信息
            skuPriceCommandDomainService.deleteSkuByInfoId(basicInfoId, sku);
        } else {
            //类目或者标签处理

        }
        log.info("营销活动:{}删除活动sku:{}成功,操作人id:{}", basicInfoId, sku, updaterId);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(key = "ActivityCommandService.updateItem:{input.basicInfoId}")
    public Long updateItem(ActivityItemConfigInput input) {
        Long basicInfoId = input.getBasicInfoId();
        input.setSystemOrigin(AuthUserUtils.getSystemOrigin().type);

        //校验活动id
        ActivityBasicInfoEntity activityBasicInfoEntity = activityBasicInfoQueryRepository.selectById(basicInfoId);
        if (activityBasicInfoEntity == null) {
            throw new BizException("活动不存在");
        }

        //改基础信息，需要校验时间重复问题
        //构建活动范围
        ActivityScopeConfigDTO activityScopeConfig = scopeConfigQueryDomainService.buildScopeConfig(basicInfoId, input.getSystemOrigin(), false);
        List<Long> scopeIds = activityScopeConfig.getScopeIds();
        if (CollectionUtil.isEmpty(scopeIds)) {
            throw new BizException("当前活动缺少活动范围，请添加活动范围");
        }

        //构建商品信息
        ActivityItemConfigDTO oldItemConfigDTO = itemConfigQueryDomainService.buildItemConfig(basicInfoId, input.getSystemOrigin(), false);

        Long itemConfigId = oldItemConfigDTO.getId();
        ActivitySkuDetailCommandParam skuDetailParams = new ActivitySkuDetailCommandParam();
        ActivitySkuDetailEntity skuDetail = null;
        if (Objects.equals(GoodSelectWayEnum.SKU.getCode(), oldItemConfigDTO.getGoodSelectWay())) {
            ActivitySkuDetailInput skuDetailDTO = input.getSkuDetailList().get(0);
            input.setGoodSelectWay(oldItemConfigDTO.getGoodSelectWay());
            skuDetailParams = ActivitySkuDetailAssembler.buildCreateParam(skuDetailDTO, oldItemConfigDTO.getId(), input.getSystemOrigin());

            //删除价格信息
            skuPriceCommandDomainService.deleteSkuByInfoId(basicInfoId, skuDetailDTO.getSku());

            //重新查询数据
            skuDetail = skuDetailQueryRepository.selectByItemConfigIdAndSku(itemConfigId, skuDetailDTO.getSku());
            if (skuDetail == null) {
                //供应商创建 需要校验是否有 待生效/生效中的活动
                ActivityBasicInfoInput infoInput = new ActivityBasicInfoInput();
                infoInput.setSystemOrigin(input.getSystemOrigin());
                infoInput.setOwnerId(activityBasicInfoEntity.getOwnerId());
                infoInput.setStartTime(activityBasicInfoEntity.getStartTime());
                infoInput.setEndTime(activityBasicInfoEntity.getEndTime());
                ActivityScopeConfigInput scopeConfigInput = new ActivityScopeConfigInput();
                scopeConfigInput.setScopeIds(scopeIds);
                RepeatActivityVO repeatActivityVO = checkRepeatActivity(infoInput, scopeConfigInput, Collections.singletonList(skuDetailDTO));
                if (repeatActivityVO != null) {
                    throw new BizException("当前活动sku存在待生效（审批中）/生效中的活动，暂不支持添加！");
                }
                skuDetail = skuDetailCommandDomainService.insert(skuDetailParams);
            } else {
                skuDetailParams.setId(skuDetail.getId());
                skuDetailCommandDomainService.update(skuDetailParams);
                skuDetail.setLadderConfig(skuDetailParams.getLadderConfig());
            }
            log.info("营销活动id:{},活动sku:{}信息配置变更", basicInfoId, skuDetailDTO.getSku());
        } else {
            //类目或者标签处理

        }

        if (Objects.equals(ScopeTypeEnum.MERCHANT_POOL.getCode(), activityScopeConfig.getScopeType())) {
            log.info("人群包活动basicInfoId:{}不需要计算活动价格", basicInfoId);
            return skuDetailParams.getId();
        }

        //异步计算活动价并保存&&供应商创建活动需要发送审批流
        ActivityScopeConfigQueryParam queryParam = new ActivityScopeConfigQueryParam();
        queryParam.setBasicInfoId(basicInfoId);
        List<ActivityScopeConfigEntity> activityScopeConfigEntities = activitySkuScopeQueryRepository.selectByCondition(queryParam);
        ActivityItemConfigEntity itemConfigEntity = ActivityItemConfigAssembler.toActivityItemConfigEntity(oldItemConfigDTO);
        activityDomainService.syncSendFeiShuApproval(activityBasicInfoEntity, activityScopeConfigEntities, Collections.singletonList(skuDetail), itemConfigEntity, Boolean.FALSE);
        return skuDetailParams.getId();
    }

    @Override
    public Boolean deleteScopeConfig(ActivityScopeConfigInput input) {
        Integer updaterId = AuthUserUtils.getBizUserId() == null ? 0 : AuthUserUtils.getBizUserId().intValue();
        Long basicInfoId = input.getBasicInfoId();
        Long scopeId = input.getScopeIds().get(0);

        //校验活动id
        ActivityBasicInfoEntity activityBasicInfoEntity = activityBasicInfoQueryRepository.selectById(basicInfoId);
        if (activityBasicInfoEntity == null) {
            throw new BizException("活动id:" + basicInfoId + "活动不存在");
        }
        // srm来源不支持删除活动范围
        if (SystemOriginEnum.SRM.getType().equals(activityBasicInfoEntity.getSystemOrigin())) {
            throw new BizException("供应商创建的活动不支持创建、修改、删除");
        }
        //临保活动不支持创建、修改
        if (Objects.equals(activityBasicInfoEntity.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }
        ActivityScopeConfigDTO activityScopeConfig = scopeConfigQueryDomainService.buildScopeConfig(
                basicInfoId, activityBasicInfoEntity.getSystemOrigin(), true);
        List<Long> oldScopeIds = activityScopeConfig.getScopeIds();
        if (oldScopeIds.size() <= 1) {
            throw new BizException("不支持删除，活动范围至少保留一条");
        }
        ActivityScopeConfigEntity scopeConfigEntity = activitySkuScopeQueryRepository.selectByScopeId(basicInfoId,
                scopeId);
        if (scopeConfigEntity == null) {
            throw new BizException("活动id:" + basicInfoId + "活动范围不存在");
        }

        ActivityScopeConfigCommandParam scopeConfig = new ActivityScopeConfigCommandParam();
        scopeConfig.setId(scopeConfigEntity.getId());
        scopeConfig.setUpdaterId(updaterId);
        scopeConfig.setDelFlag(1);
        activitySkuScopeCommandRepository.updateSelectiveById(scopeConfig);

        if (Objects.equals(scopeConfig.getScopeType(), ScopeTypeEnum.AREA.getCode())) {
            //需要将对应的sku价格都删除
            activitySkuPriceCommandRepository.deleteByAreaNos(basicInfoId,
                    Lists.newArrayList(scopeId.intValue()));
        }

        //
        if (Objects.equals(scopeConfig.getScopeType(), ScopeTypeEnum.LARGE_AREA.getCode())) {
            //需要将对应的sku价格都删除
            List<AreaSimpleDTO> areaNos = areaQueryFacade.batchQueryByLargeAreaNos(
                    Lists.newArrayList(scopeId.intValue()));
            if (CollectionUtil.isNotEmpty(areaNos)) {
                List<Integer> areaNoList = areaNos.stream().map(AreaSimpleDTO::getAreaNo).collect(Collectors.toList());
                activitySkuPriceCommandRepository.deleteByAreaNos(basicInfoId,  areaNoList);
            }
        }

        log.info("营销活动:{}删除活动范围scopeId:{}成功,操作人id:{}", basicInfoId, scopeId, updaterId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addScopeConfig(ActivityScopeConfigInput input) {
        Integer updaterId = AuthUserUtils.getBizUserId() == null ? 0 : AuthUserUtils.getBizUserId().intValue();
        Long basicInfoId = input.getBasicInfoId();
        List<Long> scopeIds = input.getScopeIds();

        //校验活动id
        ActivityBasicInfoEntity activityBasicInfoEntity = activityBasicInfoQueryRepository.selectById(basicInfoId);
        if (activityBasicInfoEntity == null) {
            throw new BizException("活动不存在");
        }
        // srm来源不支持删除活动范围
        if (SystemOriginEnum.SRM.getType().equals(activityBasicInfoEntity.getSystemOrigin())) {
            throw new BizException("供应商创建的活动不支持创建、修改、删除");
        }
        //临保活动不支持创建、修改
        if (Objects.equals(activityBasicInfoEntity.getType(), ActivityTypeEnum.NEAR_EXPIRED.getCode())) {
            throw new BizException("临保活动不支持创建、修改、删除");
        }

        ActivityScopeConfigDTO activityScopeConfig = scopeConfigQueryDomainService.buildScopeConfig(
                basicInfoId, activityBasicInfoEntity.getSystemOrigin(), true);
        Integer scopeType = activityScopeConfig.getScopeType();
        if (!Objects.equals(scopeType, input.getScopeType())) {
            throw new BizException("活动范围类型必须保持一致");
        }

        List<Long> oldScopeIds = activityScopeConfig.getScopeIds();
        boolean exist = oldScopeIds.stream().anyMatch(scopeIds::contains);
        if (exist) {
            throw new BizException("活动范围已存在，请勿重复添加");
        }

        //构建商品信息
        ActivityItemConfigDTO oldItemConfigDTO = itemConfigQueryDomainService.buildItemConfig(basicInfoId, activityBasicInfoEntity.getSystemOrigin(), false);
        ActivityScopeConfigCommandParam scopeConfig = new ActivityScopeConfigCommandParam();
        scopeConfig.setScopeId(scopeIds.get(0));
        scopeConfig.setScopeType(scopeType);
        scopeConfig.setBasicInfoId(basicInfoId);
        scopeConfig.setUpdaterId(updaterId);
        ActivityScopeConfigEntity activityScopeConfigEntity = scopeConfigCommandDomainService.insert(scopeConfig);
        log.info("营销活动:{}新增活动范围:{}成功,操作人id:{}", basicInfoId, scopeIds, updaterId);

        // 异步计算价格
        ActivityItemConfigEntity itemConfigEntity = ActivityItemConfigAssembler.toActivityItemConfigEntity(oldItemConfigDTO);
        activityDomainService.syncSendFeiShuApproval(activityBasicInfoEntity, Collections.singletonList(activityScopeConfigEntity), null, itemConfigEntity, Boolean.FALSE);
        return true;
    }

    /**
     * 活动新增、修改前校验
     *
     * @param input
     */
    public RepeatActivityVO check(ActivityInsertInput input) {
        ActivityBasicInfoInput basicInfoDTO = input.getBasicInfoDTO();
        ActivityScopeConfigInput scopeConfigDTO = input.getScopeConfigDTO();

        if (basicInfoDTO.getIsPermanent() == 0) {
            if ((basicInfoDTO.getStartTime() == null || basicInfoDTO.getEndTime() == null)) {
                throw new BizException("请填写活动时间");
            }
        }

        if (basicInfoDTO.getPlatform() != null && PlatformEnum.containLive(basicInfoDTO.getPlatform()) && Objects.equals(
                scopeConfigDTO.getScopeType(), ScopeTypeEnum.MERCHANT_POOL.getCode())) {
            throw new BizException("当前渠道不支持人群包");
        }

        //供应商需要校验待生效/已生效的活动信息
        List<ActivitySkuDetailInput> skuDetailList = input.getItemConfigDTO().getSkuDetailList();
        return checkRepeatActivity(basicInfoDTO, scopeConfigDTO, skuDetailList);
    }

    private RepeatActivityVO checkRepeatActivity(ActivityBasicInfoInput basicInfoDTO, ActivityScopeConfigInput scopeConfigDTO, List<ActivitySkuDetailInput> skuDetailList) {
        if (Objects.equals(basicInfoDTO.getSystemOrigin(), SystemOriginEnum.SRM.getType())) {
            List<String> skuList = skuDetailList.stream().map(ActivitySkuDetailInput::getSku).collect(Collectors.toList());
            ActivitySkuDetailQueryParam param = new ActivitySkuDetailQueryParam();
            param.setSystemOrigin(SystemOriginEnum.SRM.getType());
            param.setSkus(skuList);
            param.setOwnerId(basicInfoDTO.getOwnerId());
            param.setStartTime(basicInfoDTO.getStartTime());
            param.setEndTime(basicInfoDTO.getEndTime());
            param.setType(ActivityTypeEnum.SPECIAL_PRICE.getCode());
            if (Objects.equals(param.getIsPermanent(), 1)) {
                param.setStartTime(LocalDateTime.now());
                param.setEndTime(LocalDateTime.of(2099, 12, 31, 23, 59, 59));
            }
            List<ScopeQueryParam> scopeList = new ArrayList<>();
            scopeConfigDTO.getScopeIds().forEach(scopeId -> {
                ScopeQueryParam scopeQueryParam = new ScopeQueryParam();
                scopeQueryParam.setScopeType(ScopeTypeEnum.WAREHOUSE.getCode());
                scopeQueryParam.setScopeId(scopeId);
                scopeList.add(scopeQueryParam);
            });
            param.setScopeList(scopeList);
            List<ActivitySkuDetailEntity> skuDetailEntities = skuDetailQueryRepository.listBySkusAndOwnerId(param);
            if (!CollectionUtils.isEmpty(skuDetailEntities)) {
                RepeatActivityVO repeatActivityVO = new RepeatActivityVO();
                List<RepeatActivitySkuVO> repeatList = new ArrayList<>();
                skuDetailEntities.forEach(entity -> {
                    RepeatActivitySkuVO repeatActivitySkuVO = new RepeatActivitySkuVO();
                    repeatActivitySkuVO.setSku(entity.getSku());
                    repeatList.add(repeatActivitySkuVO);
                });
                repeatActivityVO.setRepeatList(repeatList);
                List<Long> basicInfoIds = skuDetailEntities.stream().map(ActivitySkuDetailEntity::getBasicInfoId).collect(Collectors.toList());
                log.info("待生效/生效中的活动ID:{}", JSON.toJSONString(basicInfoIds));
                return repeatActivityVO;
            }
        }
        return null;
    }

    private void updateCheck(ActivityBasicInfoUpdateInput input, ActivityScopeConfigDTO activityScopeConfig) {
        if (input.getIsPermanent() == 0) {
            if ((input.getStartTime() == null || input.getEndTime() == null)) {
                throw new BizException("请填写活动时间");
            }
        }

        if (input.getPlatform() != null && PlatformEnum.containLive(input.getPlatform()) &&
                Objects.equals(input.getScopeType(), ScopeTypeEnum.MERCHANT_POOL.getCode())) {
            throw new BizException("当前渠道不支持人群包");
        }

        //校验当前活动范围是否有相同的活动
        if (Objects.equals(input.getSystemOrigin(), SystemOriginEnum.SRM.getType())) {
            BatchCheckQueryParam param = new BatchCheckQueryParam();
            param.setSystemOrigin(SystemOriginEnum.SRM.getType());
            param.setOwnerId(input.getOwnerId());
            param.setStartTime(input.getStartTime());
            param.setEndTime(input.getEndTime());
            param.setType(ActivityTypeEnum.SPECIAL_PRICE.getCode());
            if (Objects.equals(param.getIsPermanent(), 1)) {
                param.setStartTime(LocalDateTime.now());
                param.setEndTime(LocalDateTime.of(2099, 12, 31, 23, 59, 59));
            }
            List<ScopeQueryParam> scopeList = new ArrayList<>();
            activityScopeConfig.getScopeIds().forEach(scopeId -> {
                ScopeQueryParam scopeQueryParam = new ScopeQueryParam();
                scopeQueryParam.setScopeType(ScopeTypeEnum.WAREHOUSE.getCode());
                scopeQueryParam.setScopeId(scopeId);
                scopeList.add(scopeQueryParam);
            });
            param.setScopeList(scopeList);
            List<ActivityItemScopeEntity> validActivityList = activityBasicInfoQueryRepository.selectByScope(param);
            if (!CollectionUtils.isEmpty(validActivityList)) {
                throw new BizException("修改活动失败，当前生效时间区间有重复的活动！");
            }
        }
    }

    private void supplierScopeConfig(ActivityInsertInput input, ActivityBasicInfoCommandParam param, ActivityBasicInfoEntity basicInfoEntity, ActivityScopeConfigInput scopeConfigDTO, List<ActivityScopeConfigCommandParam> scopeConfigParams, ActivityItemConfigInput itemConfigDTO) {
        if (Objects.equals(param.getSystemOrigin(), SystemOriginEnum.SRM.getType())) {
            if (scopeConfigDTO.getScopeIds().size() > 1) {
                throw new BizException("供应商创建活动只能选择一个仓维度");
            }

            //维度转换成运营大区维度
            List<SkuWarehouseNoQueryAreaInput> inputs = new ArrayList<>();
            itemConfigDTO.getSkuDetailList().forEach(x -> {
                SkuWarehouseNoQueryAreaInput areaInput = new SkuWarehouseNoQueryAreaInput();
                areaInput.setSku(x.getSku());
                areaInput.setWarehouseNo(scopeConfigDTO.getScopeIds().get(0).intValue());
                inputs.add(areaInput);
            });
            List<AreaWarehouseNoSkuDTO> areaWarehouseNoSkuDTOS = wncDeliveryFenceQueryFacade.queryAreaByListWarehouseAndSku(inputs);
            if (CollectionUtils.isEmpty(areaWarehouseNoSkuDTOS)) {
                throw new BizException("创建活动失败，当前库存仓暂无可售卖区域");
            }
            Set<Integer> areaNos = areaWarehouseNoSkuDTOS.stream().map(AreaWarehouseNoSkuDTO::getAreaNos).filter(Objects::nonNull)
                    .flatMap(List::stream).collect(Collectors.toSet());
            areaNos.forEach(x -> {
                ActivityScopeConfigCommandParam scopeConfigParam = new ActivityScopeConfigCommandParam();
                scopeConfigParam.setBasicInfoId(basicInfoEntity.getId());
                scopeConfigParam.setScopeId(x.longValue());
                scopeConfigParam.setScopeType(ScopeTypeEnum.AREA.getCode());
                scopeConfigParam.setUpdaterId(input.getCreatorId().intValue());
                scopeConfigParam.setDelFlag(CommonStatus.NO.getCode());
                scopeConfigParams.add(scopeConfigParam);
            });
        }
    }
}
