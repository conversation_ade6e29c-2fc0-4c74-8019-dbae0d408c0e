package net.xianmu.marketing.center.application.inbound.controller.tabsort.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2024/1/8
 */
@Data
public class CombinationPageVO implements Serializable {

    /**
     * 组合id
     */
    private Long id;

    /**
     * 组合名称
     */
    private String name;

    /**
     * 范围类型
     */
    private Integer scopeType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creatorName;

}
