package net.xianmu.marketing.center.application.inbound.controller.user;

import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.application.inbound.controller.bill.assembler.AppCustYearBillDiAssembler;
import net.xianmu.marketing.center.application.inbound.controller.bill.vo.AppCustYearBillDiVO;
import net.xianmu.marketing.center.application.inbound.controller.user.vo.CurrentUserInfo;
import org.apache.shiro.SecurityUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Title 客户年账单表
 * @Description 客户年账单表功能模块
 * <AUTHOR>
 * @date 2024-05-30 10:27:32
 * @version 1.0
 */
@RestController
@RequestMapping(value="/current/user")
public class CurrentUserController {


	/**
	* 获取详情
	* @return
	*/
	@GetMapping(value = "/query/user-info")
	public CommonResult<CurrentUserInfo> userInfo(){
		ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
		if (user == null) {
			throw new BizException("未获取到登录信息，请重新登录");
		}
		return CommonResult.ok(buildCurrentUserInfo(user));
	}


	private CurrentUserInfo buildCurrentUserInfo(ShiroUser user) {
		CurrentUserInfo currentUserInfo = new CurrentUserInfo();
		currentUserInfo.setAutUserId(user.getId());
		currentUserInfo.setTenantId(user.getTenantId());
		currentUserInfo.setBizUserId(user.getBizUserId());
		currentUserInfo.setSystemOrigin(user.getSystemOrigin());
		currentUserInfo.setSystemOriginType(SystemOriginEnum.getSystemOriginByName(user.getSystemOrigin()).getType());
		currentUserInfo.setAuthUserBaseId(user.getBaseUserId());
		currentUserInfo.setUsername(user.getUsername());
		currentUserInfo.setPhone(user.getPhone());
		currentUserInfo.setNickname(user.getNickname());
		if(SystemOriginEnum.SRM.getName().equals(user.getSystemOrigin())) {
			currentUserInfo.setSupplierId(-1L);
			currentUserInfo.setSupplierName("");
		}
		return currentUserInfo;
	}

}

