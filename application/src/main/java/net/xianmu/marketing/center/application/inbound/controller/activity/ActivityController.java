package net.xianmu.marketing.center.application.inbound.controller.activity;

import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.controller.AuthBaseController;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.application.inbound.controller.activity.input.*;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.*;
import net.xianmu.marketing.center.application.service.activity.ActivityCommandService;
import net.xianmu.marketing.center.application.service.activity.ActivityQueryService;
import net.xianmu.marketing.center.common.constant.Global;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 特价校验重复活动
 *
 * @author: <EMAIL>
 * @create: 2023/12/25
 */
@RequestMapping("/activity")
@RestController
public class ActivityController extends AuthBaseController {

    @Resource
    private ActivityQueryService activityQueryService;

    @Resource
    private ActivityCommandService activityCommandService;

    /**
     * 批量查询sku生效中的活动
     *
     * @param input
     * @return
     */
    @PostMapping("/query/repeat/check")
    public CommonResult<RepeatActivityVO> listRepeatActivity(@RequestBody @Validated BatchCheckInput input) {
        RepeatActivityVO activityVO = activityQueryService.listRepeatActivity(input);
        return CommonResult.ok(activityVO);
    }

    /**
     * 批量删除sku
     *
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @PostMapping("/upsert/delete/batch/item")
    public CommonResult<Boolean> batchDeleteItem(@RequestBody @Validated BatchDeleteInput input) {
        Boolean result = activityCommandService.batchDeleteItem(input);
        return CommonResult.ok(result);
    }

    /**
     * 创建活动
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add/basic-info", method = RequestMethod.POST)
    public CommonResult<RepeatActivityVO> addBasicInfo(@Validated @RequestBody ActivityInsertInput input) {
        input.setCreatorId(getBizUserId());
        input.getBasicInfoDTO().setSystemOrigin(getSystemOrigin().type);
        return CommonResult.ok(activityCommandService.addBasicInfo(input));
    }

    /**
     * 修改活动基础信息
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/update/basic-info", method = RequestMethod.POST)
    public CommonResult<Boolean> updateBasicInfo(@Validated @RequestBody ActivityBasicInfoUpdateInput input) {
        input.setUpdaterId(getBizUserId());
        input.setSystemOrigin(getSystemOrigin().type);
        return CommonResult.ok(activityCommandService.updateBasicInfo(input));
    }

    /**
     * 删除活动
     * @param basicInfoId
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete", method = RequestMethod.POST)
    public CommonResult<Boolean> delete(Long basicInfoId) {
        return CommonResult.ok(activityCommandService.delete(basicInfoId, getBizUserId()));
    }

    /**
     * 新增活动范围
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add/scope-config", method = RequestMethod.POST)
    public CommonResult<Boolean> addScopeConfig(@Validated @RequestBody ActivityScopeConfigInput input) {
        input.setUpdaterId(getBizUserId());
        return CommonResult.ok(activityCommandService.addScopeConfig(input));
    }

    /**
     * 删除活动范围
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete/scope-config", method = RequestMethod.POST)
    public CommonResult<Boolean> deleteScopeConfig(@RequestBody ActivityScopeConfigInput input) {
        input.setUpdaterId(getBizUserId());
        return CommonResult.ok(activityCommandService.deleteScopeConfig(input));
    }

    /**
     * 新增、修改活动商品
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/update/item", method = RequestMethod.POST)
    public CommonResult<Long> updateItem(@Validated @RequestBody ActivityItemConfigInput input) {
        input.setUpdaterId(getBizUserId());
        return CommonResult.ok(activityCommandService.updateItem(input));
    }

    /**
     * 删除活动商品
     * @param basicInfoId
     * @param sku
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete/item", method = RequestMethod.POST)
    public CommonResult<Boolean> deleteItem(Long basicInfoId, String sku) {
        return CommonResult.ok(activityCommandService.deleteItem(basicInfoId, sku, getBizUserId()));
    }

    /**
     * 获取活动详情
     * @param basicInfoId
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/query/detail", method = RequestMethod.POST)
    public CommonResult<ActivityDetailVO> getDetail(Long basicInfoId) {
        return CommonResult.ok(activityQueryService.getDetail(basicInfoId, getSystemOrigin().type));
    }

    /**
     * 活动开启、关闭
     * @param basicInfoId
     * @param status
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/open-down", method = RequestMethod.POST)
    public CommonResult<Boolean> openDown(Long basicInfoId, Integer status) {
        return CommonResult.ok(activityCommandService.openDown(basicInfoId, status, getBizUserId()));
    }

    /**
     * 活动列表页查询
     * admin来源能查全部活动
     * srm来源只能看供应商自己创建的
     * @param pageQueryDTO
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/query/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<ActivityPageRespVO>> page(@RequestBody ActivityPageQueryInput pageQueryDTO) {
        return CommonResult.ok(activityQueryService.page(pageQueryDTO));
    }

    /**
     * 活动商品价格明细列表
     * @param basicInfoId
     * @param sku
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/query/sku/price", method = RequestMethod.POST)
    public CommonResult<List<LargeAreaSkuPriceVO>> listSkuPrice(Long basicInfoId, String sku) {
        return CommonResult.ok(activityQueryService.listSkuPrice(basicInfoId, sku));
    }

    /**
     * 批量添加活动sku
     * @param input
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add/batch-sku", method = RequestMethod.POST)
    public CommonResult<ActivitySkuBatchVO> batchAddSku(@RequestBody @Validated ActivityBatchSkuInput input) {
        return CommonResult.ok(activityCommandService.batchAddSku(input));
    }

    /**
     * 开启/关闭自动定价
     * @param input
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA, "srm:activity:insert-update"}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/onOff/autoPrice", method = RequestMethod.POST)
    public CommonResult<Boolean> automaticPrice(@RequestBody @Validated ActivityItemConfigInput input) {
        input.setUpdaterId(getBizUserId());
        return CommonResult.ok(activityCommandService.automaticPrice(input));
    }

}
