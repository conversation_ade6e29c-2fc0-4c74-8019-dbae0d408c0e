package net.xianmu.marketing.center.application.inbound.controller.activity.assembler;


import net.xianmu.marketing.center.application.inbound.controller.activity.input.ActivityItemConfigInput;
import net.xianmu.marketing.center.application.inbound.controller.activity.vo.ActivityItemConfigVO;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.domain.activity.dto.ActivityItemConfigDTO;
import net.xianmu.marketing.center.domain.activity.entity.ActivityItemConfigEntity;
import net.xianmu.marketing.center.domain.activity.param.query.ActivityItemConfigQueryParam;
import net.xianmu.marketing.center.domain.activity.param.command.ActivityItemConfigCommandParam;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Collections;

/**
 *
 * <AUTHOR>
 * @date 2025-06-10 15:41:35
 * @version 1.0
 *
 */
public class ActivityItemConfigAssembler {

    private ActivityItemConfigAssembler() {
        // 无需实现
    }

    public static ActivityItemConfigCommandParam buildCreateParam(ActivityItemConfigInput activityItemConfigCommandInput) {
        if (activityItemConfigCommandInput == null) {
            return null;
        }
        ActivityItemConfigCommandParam activityItemConfigCommandParam = new ActivityItemConfigCommandParam();
        activityItemConfigCommandParam.setId(activityItemConfigCommandInput.getId());
        activityItemConfigCommandParam.setBasicInfoId(activityItemConfigCommandInput.getBasicInfoId());
        activityItemConfigCommandParam.setGoodSelectWay(activityItemConfigCommandInput.getGoodSelectWay());
        activityItemConfigCommandParam.setPricingType(activityItemConfigCommandInput.getPricingType());
        activityItemConfigCommandParam.setPricingTypeExt(activityItemConfigCommandInput.getPricingTypeExt());
        activityItemConfigCommandParam.setDiscountPercentage(activityItemConfigCommandInput.getDiscountPercentage());
        activityItemConfigCommandParam.setDiscount(activityItemConfigCommandInput.getDiscount());
        if (activityItemConfigCommandInput.getUpdaterId() != null) {
            activityItemConfigCommandParam.setUpdaterId(activityItemConfigCommandInput.getUpdaterId().intValue());
        }
        activityItemConfigCommandParam.setDelFlag(CommonStatus.NO.getCode());
        activityItemConfigCommandParam.setCreateTime(LocalDateTime.now());
        return activityItemConfigCommandParam;
    }


    public static ActivityItemConfigCommandParam buildUpdateParam(ActivityItemConfigInput activityItemConfigCommandInput) {
        if (activityItemConfigCommandInput == null) {
            return null;
        }
        ActivityItemConfigCommandParam activityItemConfigCommandParam = new ActivityItemConfigCommandParam();
        activityItemConfigCommandParam.setId(activityItemConfigCommandInput.getId());
        activityItemConfigCommandParam.setBasicInfoId(activityItemConfigCommandInput.getBasicInfoId());
        activityItemConfigCommandParam.setGoodSelectWay(activityItemConfigCommandInput.getGoodSelectWay());
        activityItemConfigCommandParam.setPricingType(activityItemConfigCommandInput.getPricingType());
        activityItemConfigCommandParam.setPricingTypeExt(activityItemConfigCommandInput.getPricingTypeExt());
        activityItemConfigCommandParam.setDiscountPercentage(activityItemConfigCommandInput.getDiscountPercentage());
        activityItemConfigCommandParam.setDiscount(activityItemConfigCommandInput.getDiscount());
        if (activityItemConfigCommandInput.getUpdaterId() != null) {
            activityItemConfigCommandParam.setUpdaterId(activityItemConfigCommandInput.getUpdaterId().intValue());
        }
        return activityItemConfigCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<ActivityItemConfigVO> toActivityItemConfigVOList(List<ActivityItemConfigEntity> activityItemConfigEntityList) {
        if (activityItemConfigEntityList == null) {
            return Collections.emptyList();
        }
        List<ActivityItemConfigVO> activityItemConfigVOList = new ArrayList<>();
        for (ActivityItemConfigEntity activityItemConfigEntity : activityItemConfigEntityList) {
            activityItemConfigVOList.add(toActivityItemConfigVO(activityItemConfigEntity));
        }
        return activityItemConfigVOList;
}


   public static ActivityItemConfigVO toActivityItemConfigVO(ActivityItemConfigEntity activityItemConfigEntity) {
       if (activityItemConfigEntity == null) {
            return null;
       }
       ActivityItemConfigVO activityItemConfigVO = new ActivityItemConfigVO();
       activityItemConfigVO.setId(activityItemConfigEntity.getId());
       activityItemConfigVO.setBasicInfoId(activityItemConfigEntity.getBasicInfoId());
       activityItemConfigVO.setGoodSelectWay(activityItemConfigEntity.getGoodSelectWay());
       activityItemConfigVO.setPricingType(activityItemConfigEntity.getPricingType());
       activityItemConfigVO.setPricingTypeExt(activityItemConfigEntity.getPricingTypeExt());
       activityItemConfigVO.setDiscountPercentage(activityItemConfigEntity.getDiscountPercentage());
       activityItemConfigVO.setDiscount(activityItemConfigEntity.getDiscount());
       activityItemConfigVO.setUpdaterId(activityItemConfigEntity.getUpdaterId());
       activityItemConfigVO.setDelFlag(activityItemConfigEntity.getDelFlag());
       activityItemConfigVO.setCreateTime(activityItemConfigEntity.getCreateTime());
       activityItemConfigVO.setUpdateTime(activityItemConfigEntity.getUpdateTime());
       return activityItemConfigVO;
   }

    public static ActivityItemConfigVO toActivityItemConfigVO2(ActivityItemConfigDTO activityItemConfigEntity) {
        if (activityItemConfigEntity == null) {
            return null;
        }
        ActivityItemConfigVO activityItemConfigVO = new ActivityItemConfigVO();
        activityItemConfigVO.setId(activityItemConfigEntity.getId());
        activityItemConfigVO.setBasicInfoId(activityItemConfigEntity.getBasicInfoId());
        activityItemConfigVO.setGoodSelectWay(activityItemConfigEntity.getGoodSelectWay());
        activityItemConfigVO.setPricingType(activityItemConfigEntity.getPricingType());
        activityItemConfigVO.setPricingTypeExt(activityItemConfigEntity.getPricingTypeExt());
        activityItemConfigVO.setDiscountPercentage(activityItemConfigEntity.getDiscountPercentage());
        activityItemConfigVO.setDiscount(activityItemConfigEntity.getDiscount());
        activityItemConfigVO.setUpdaterId(activityItemConfigEntity.getUpdaterId());
        activityItemConfigVO.setDelFlag(activityItemConfigEntity.getDelFlag());
        activityItemConfigVO.setCreateTime(activityItemConfigEntity.getCreateTime());
        activityItemConfigVO.setUpdateTime(activityItemConfigEntity.getUpdateTime());
        return activityItemConfigVO;
    }

    public static ActivityItemConfigEntity toActivityItemConfigEntity(ActivityItemConfigDTO oldItemConfigDTO) {
        if (oldItemConfigDTO == null) {
            return null;
        }
        ActivityItemConfigEntity activityItemConfigEntity = new ActivityItemConfigEntity();
        activityItemConfigEntity.setId(oldItemConfigDTO.getId());
        activityItemConfigEntity.setBasicInfoId(oldItemConfigDTO.getBasicInfoId());
        activityItemConfigEntity.setGoodSelectWay(oldItemConfigDTO.getGoodSelectWay());
        activityItemConfigEntity.setPricingType(oldItemConfigDTO.getPricingType());
        activityItemConfigEntity.setPricingTypeExt(oldItemConfigDTO.getPricingTypeExt());
        activityItemConfigEntity.setDiscountPercentage(oldItemConfigDTO.getDiscountPercentage());
        activityItemConfigEntity.setDiscount(oldItemConfigDTO.getDiscount());
        activityItemConfigEntity.setUpdaterId(oldItemConfigDTO.getUpdaterId());
        activityItemConfigEntity.setDelFlag(oldItemConfigDTO.getDelFlag());
        activityItemConfigEntity.setCreateTime(oldItemConfigDTO.getCreateTime());
        activityItemConfigEntity.setUpdateTime(oldItemConfigDTO.getUpdateTime());
        return activityItemConfigEntity;
    }
}
