package net.xianmu.marketing.center.application.common.util;


import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.marketing.center.application.inbound.controller.user.vo.CurrentUserInfo;
import org.apache.shiro.SecurityUtils;

/**
 * @description 用户信息
 */
@Slf4j
public class UserInfoHolder {

    private static final ThreadLocal<CurrentUserInfo> ADMIN_ENTITY_THREAD_LOCAL = new ThreadLocal<>();


    /**
     * 获取当前登录用户对象
     *
     * @return
     */
    public static CurrentUserInfo getCurrentUser() {
        if (ADMIN_ENTITY_THREAD_LOCAL.get() == null) {
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            if (user == null) {
                log.warn("用户没有登录...");
                return null;
            }
            ADMIN_ENTITY_THREAD_LOCAL.set(buildCurrentUserInfo(user));
        }
        return ADMIN_ENTITY_THREAD_LOCAL.get();
    }


    private static CurrentUserInfo buildCurrentUserInfo(ShiroUser user) {
        CurrentUserInfo currentUserInfo = new CurrentUserInfo();
        currentUserInfo.setAutUserId(user.getId());
        currentUserInfo.setTenantId(user.getTenantId());
        currentUserInfo.setBizUserId(user.getBizUserId());
        currentUserInfo.setSystemOrigin(user.getSystemOrigin());
        currentUserInfo.setSystemOriginType(SystemOriginEnum.getSystemOriginByName(user.getSystemOrigin()).getType());
        currentUserInfo.setAuthUserBaseId(user.getBaseUserId());
        currentUserInfo.setUsername(user.getUsername());
        currentUserInfo.setPhone(user.getPhone());
        currentUserInfo.setNickname(user.getNickname());
        if(SystemOriginEnum.SRM.getName().equals(user.getSystemOrigin())) {
            currentUserInfo.setSupplierId(-1L);
            currentUserInfo.setSupplierName("");
        }
        return currentUserInfo;
    }




    public static void clear() {
        ADMIN_ENTITY_THREAD_LOCAL.remove();
    }
}
