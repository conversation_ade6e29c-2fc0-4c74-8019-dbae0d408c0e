<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>marketing-center</artifactId>
        <groupId>net.xianmu</groupId>
        <version>1.0.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>marketing-center-facade</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    
    <dependencies>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>marketing-center-common</artifactId>
        </dependency>


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>net.manage.client</groupId>
            <artifactId>manage-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>sf-mall-manage-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>message-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-client</artifactId>
        </dependency>
        
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>mall-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-redis-support</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>common-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>summerfarm-inventory-client</artifactId>
        </dependency>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- alibaba开源数据库连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
    </dependencies>
</project>
