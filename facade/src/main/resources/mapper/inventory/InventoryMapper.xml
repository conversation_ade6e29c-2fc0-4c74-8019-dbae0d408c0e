<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.marketing.center.facade.mapper.InventoryMapper">
  <select id="selectSkuBaseInfosBySku" resultType="net.xianmu.marketing.center.facade.item.dto.SkuInfoDetailDTO">
    select
    i.inv_id invId, i.sku, i.pd_id pdId, p.pd_name pdName, p.picture_path picturePath, i.weight, i.volume, i.weight_num weightNum,
    p.storage_location storageArea, i.unit unit, p.category_id categoryId, i.type pdAttribute, i.ext_type extType, i.outdated, i.admin_id adminId,
    i.sku_name skuName, i.sku_pic skuPic
    from
    inventory i left join products p on i.pd_id = p.pd_id
    where
    i.sku in
    <foreach collection="skus" open="(" separator="," close=")" item="el">
      #{el}
    </foreach>

  </select>


  <select id="selectSkuBaseInfosByIds" resultType="net.xianmu.marketing.center.facade.item.dto.SkuInfoDetailDTO">
    select
    i.inv_id skuId, i.sku, p.category_id categoryId, i.sub_type subType
    from
    inventory i left join products p on i.pd_id = p.pd_id
    where
    i.inv_id in
    <foreach collection="ids" open="(" separator="," close=")" item="el">
      #{el}
    </foreach>
  </select>
</mapper>