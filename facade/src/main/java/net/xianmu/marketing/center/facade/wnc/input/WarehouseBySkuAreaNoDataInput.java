package net.xianmu.marketing.center.facade.wnc.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/11 16:02
 * @PackageName:net.xianmu.marketing.center.facade.wnc.input
 * @ClassName: WarehouseBySkuAreaNoDataInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class WarehouseBySkuAreaNoDataInput implements Serializable {

    private static final long serialVersionUID = -428316009485194283L;

    private String sku;

    /**
     * 区域编号集合
     */
    private List<Integer> areaNoList;
}
