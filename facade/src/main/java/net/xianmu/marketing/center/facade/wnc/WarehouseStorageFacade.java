package net.xianmu.marketing.center.facade.wnc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseBaseInfoByNoReq;
import net.summerfarm.wnc.client.resp.WarehouseBaseInfoByNoResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.facade.wnc.converter.WncConverter;
import net.xianmu.marketing.center.facade.wnc.dto.WarehouseBaseInfoByNoDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/13 10:59
 * @PackageName:net.xianmu.marketing.center.facade.wnc
 * @ClassName: WarehouseStorageFacade
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@Component
public class WarehouseStorageFacade {

    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;

    public Map<Integer, String> queryWarehouseBaseInfoByNo(List<Integer> warehouseNos) {
        if (CollectionUtils.isEmpty(warehouseNos)) {
            return Collections.emptyMap();
        }
        WarehouseBaseInfoByNoReq req = new WarehouseBaseInfoByNoReq();
        req.setWarehouseNos(warehouseNos);
        DubboResponse<List<WarehouseBaseInfoByNoResp>> dubboResponse = warehouseStorageQueryProvider.queryBaseInfoByWarehouseNo(req);
        if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
            log.error("WncDeliveryFenceQueryFacade[]queryDeliveryFence[]queryDeliveryFence[]error!");
            return null;
        }
        return WncConverter.toWarehouseBaseInfoByNoDTOs(dubboResponse.getData());
    }

}
