package net.xianmu.marketing.center.facade.area;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.area.AreaQueryProvider;
import net.summerfarm.client.resp.area.AreaSimpleResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.common.enums.merchantpool.CommonStatus;
import net.xianmu.marketing.center.facade.area.converter.AreaConverter;
import net.xianmu.marketing.center.facade.area.dto.AreaSimpleDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/26
 */
@Slf4j
@Component
public class AreaQueryFacade {

    @DubboReference
    private AreaQueryProvider areaQueryProvider;

    /**
     * 根据运营区域批量查询
     * @param areaNos
     * @return
     */
    public List<AreaSimpleDTO> batchQueryByAreaNos(List<Integer> areaNos) {
        List<AreaSimpleDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(areaNos)) {
            return list;
        }
        try {
            DubboResponse<List<AreaSimpleResp>> dubboResponse = areaQueryProvider.batchQueryByAreaNos(areaNos);
            if (dubboResponse != null && dubboResponse.isSuccess()) {
                List<AreaSimpleResp> areaSimpleResps = dubboResponse.getData();
                list = AreaConverter.toAreaSimpleDTOList(areaSimpleResps);
            }
        } catch (Exception e) {
            log.error("根据运营区域批量查询异常,areaNos:{}, cause:{}", areaNos, Throwables.getStackTraceAsString(e));
        }
        return list;
    }

    /**
     * 根据运营区域批量查询--有效的
     * @param areaNos
     * @return
     */
    public List<AreaSimpleDTO> batchQueryEffectiveByAreaNos(List<Integer> areaNos) {
        List<AreaSimpleDTO> batchQueryByAreaNos = this.batchQueryByAreaNos(areaNos);
        batchQueryByAreaNos = batchQueryByAreaNos.stream().filter(x -> Objects.equals(x.getAreaStatus(),
                CommonStatus.YES.getCode())).collect(Collectors.toList());
        return batchQueryByAreaNos;
    }

    /**
     * 根据运营大区批量查询
     * @param largeAreaNos
     * @return
     */
    public List<AreaSimpleDTO> batchQueryByLargeAreaNos(List<Integer> largeAreaNos) {
        List<AreaSimpleDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(largeAreaNos)) {
            return list;
        }
        try {
            DubboResponse<List<AreaSimpleResp>> dubboResponse = areaQueryProvider.batchQueryByLargeAreaNos(largeAreaNos);
            if (dubboResponse != null && dubboResponse.isSuccess()) {
                List<AreaSimpleResp> areaSimpleResps = dubboResponse.getData();
                list = AreaConverter.toAreaSimpleDTOList(areaSimpleResps);
            }
        } catch (Exception e) {
            log.error("根据运营大区批量查询异常, largeAreaNos:{}, cause:{}", largeAreaNos, Throwables.getStackTraceAsString(e));
        }
        return list;
    }

    /**
     * 根据运营大区批量查询--有效的
     * @param largeAreaNos
     * @return
     */
    public List<AreaSimpleDTO> batchQueryEffectiveByLargeAreaNos(List<Integer> largeAreaNos) {
        List<AreaSimpleDTO> areaSimpleDTOS = this.batchQueryByLargeAreaNos(largeAreaNos);
        List<AreaSimpleDTO> list = areaSimpleDTOS.stream().filter(x -> Objects.equals(x.getAreaStatus(), CommonStatus.YES.getCode())
                && Objects.equals(x.getLargeAreaStatus(), CommonStatus.YES.getCode())).collect(Collectors.toList());
        return list;
    }

}
