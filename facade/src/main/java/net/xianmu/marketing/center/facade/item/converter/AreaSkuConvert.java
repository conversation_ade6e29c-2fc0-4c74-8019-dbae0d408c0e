package net.xianmu.marketing.center.facade.item.converter;

import net.summerfarm.client.req.areasku.AreaSkuQueryReq;
import net.summerfarm.client.resp.areasku.AreaSkuResp;
import net.xianmu.marketing.center.facade.item.dto.AreaSkuDTO;
import net.xianmu.marketing.center.facade.item.input.AreaSkuQueryInput;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/11 18:08
 * @PackageName:net.xianmu.marketing.center.facade.item.converter
 * @ClassName: AreaSkuConvert
 * @Description: TODO
 * @Version 1.0
 */
public class AreaSkuConvert {
    public static List<AreaSkuQueryReq> toAreaSkuQueryReqs(List<AreaSkuQueryInput> inputs) {
        List<AreaSkuQueryReq> reqList = new ArrayList<>();
        for (AreaSkuQueryInput input : inputs) {
            AreaSkuQueryReq req = new AreaSkuQueryReq();
            req.setSku(input.getSku());
            req.setAreaNos(input.getAreaNos());
            reqList.add(req);
        }
        return reqList;
    }

    public static List<AreaSkuDTO> toAreaSkuDTOList(List<AreaSkuResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        List<AreaSkuDTO> areaSkuDTOS = new ArrayList<>();
        for (AreaSkuResp areaSkuResp : data) {
            AreaSkuDTO areaSkuDTO = new AreaSkuDTO();
            areaSkuDTO.setSku(areaSkuResp.getSku());
            areaSkuDTO.setAreaNo(areaSkuResp.getAreaNo());
            areaSkuDTO.setPrice(areaSkuResp.getPrice());
            areaSkuDTOS.add(areaSkuDTO);
        }
        return areaSkuDTOS;
    }
}
