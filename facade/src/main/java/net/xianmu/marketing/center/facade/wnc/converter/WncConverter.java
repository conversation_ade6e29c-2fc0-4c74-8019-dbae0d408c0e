package net.xianmu.marketing.center.facade.wnc.converter;

import net.summerfarm.wnc.client.req.DeliveryFenceQueryReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.req.fence.AreaQueryWarehouseNoSkuReq;
import net.summerfarm.wnc.client.req.fence.SkuWarehouseNoQueryAreaReq;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.WarehouseBaseInfoByNoResp;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.summerfarm.wnc.client.resp.fence.AreaWarehouseNoSkuResp;
import net.xianmu.marketing.center.facade.wnc.dto.AreaWarehouseNoSkuDTO;
import net.xianmu.marketing.center.facade.wnc.dto.DeliveryFenceDTO;
import net.xianmu.marketing.center.facade.wnc.dto.WarehouseBaseInfoByNoDTO;
import net.xianmu.marketing.center.facade.wnc.dto.WarehouseBySkuAreaNoDTO;
import net.xianmu.marketing.center.facade.wnc.input.DeliveryFenceQueryInput;
import net.xianmu.marketing.center.facade.wnc.input.SkuWarehouseNoQueryAreaInput;
import net.xianmu.marketing.center.facade.wnc.input.WarehouseBySkuAreaNoDataInput;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName WncConverter
 * @Description
 * <AUTHOR>
 * @Date 15:23 2024/4/9
 * @Version 1.0
 **/
public class WncConverter {

    public static DeliveryFenceQueryReq toDeliveryFenceQueryReq(DeliveryFenceQueryInput input) {
        if (input == null) {
            return null;
        }
        DeliveryFenceQueryReq req = new DeliveryFenceQueryReq();
        req.setArea(input.getArea());
        req.setCity(input.getCity());

        //目前写死 因为wnc有限制 这是必填字段
        req.setPoi("poi");
        return req;
    }

    public static DeliveryFenceDTO toDeliveryFenceDTO(DeliveryFenceResp data) {
        if (data == null) {
            return null;
        }
        DeliveryFenceDTO deliveryFenceDTO = new DeliveryFenceDTO();
        deliveryFenceDTO.setFenceId(data.getFenceId());
        deliveryFenceDTO.setFenceName(data.getFenceName());
        deliveryFenceDTO.setAreaNo(data.getAreaNo());
        deliveryFenceDTO.setCityName(data.getCityName());
        deliveryFenceDTO.setStatus(data.getStatus());
        deliveryFenceDTO.setStoreNo(data.getStoreNo());
        deliveryFenceDTO.setOrderChannelType(data.getOrderChannelType());
        return deliveryFenceDTO;
    }

    public static WarehouseBySkuAreaNoQueryReq toWarehouseBySkuAreaNoQueryReq(List<WarehouseBySkuAreaNoDataInput> inputs) {
        if (CollectionUtils.isEmpty(inputs)) {
            return null;
        }
        WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq();
        List<WarehouseBySkuAreaNoDataReq> areaSkuList = new ArrayList<>();
        for (WarehouseBySkuAreaNoDataInput input : inputs) {
            WarehouseBySkuAreaNoDataReq dataReq = new WarehouseBySkuAreaNoDataReq();
            dataReq.setSku(input.getSku());
            dataReq.setAreaNoList(input.getAreaNoList());
            areaSkuList.add(dataReq);
        }
        req.setAreaSkuList(areaSkuList);
        return req;
    }

    public static List<WarehouseBySkuAreaNoDTO> toWarehouseBySkuAreaNoDTOs(List<WarehouseBySkuAreaNoResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        List<WarehouseBySkuAreaNoDTO> warehouseBySkuAreaNoDTOS = new ArrayList<>();
        for (WarehouseBySkuAreaNoResp bySkuAreaNoResp : data) {
            WarehouseBySkuAreaNoDTO warehouseBySkuAreaNoDTO = new WarehouseBySkuAreaNoDTO();
            warehouseBySkuAreaNoDTO.setWarehouseNo(bySkuAreaNoResp.getWarehouseNo());
            warehouseBySkuAreaNoDTO.setWarehouseName(bySkuAreaNoResp.getWarehouseName());
            warehouseBySkuAreaNoDTO.setSku(bySkuAreaNoResp.getSku());
            warehouseBySkuAreaNoDTO.setAreaNo(bySkuAreaNoResp.getAreaNo());
            warehouseBySkuAreaNoDTOS.add(warehouseBySkuAreaNoDTO);
        }
        return warehouseBySkuAreaNoDTOS;
    }

    public static Map<Integer, String> toWarehouseBaseInfoByNoDTOs(List<WarehouseBaseInfoByNoResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }
        return data.stream().collect(Collectors.toMap(WarehouseBaseInfoByNoResp::getWarehouseNo, WarehouseBaseInfoByNoResp::getWarehouseName));
    }

    public static AreaQueryWarehouseNoSkuReq toAreaQueryWarehouseNoSkuReq(List<SkuWarehouseNoQueryAreaInput> inputs) {
        AreaQueryWarehouseNoSkuReq req = new AreaQueryWarehouseNoSkuReq();
        List<SkuWarehouseNoQueryAreaReq> skuWarehouseNoQueryAreaReqList = new ArrayList<>();

        inputs.forEach(input -> {
            SkuWarehouseNoQueryAreaReq req1 = new SkuWarehouseNoQueryAreaReq();
            req1.setSku(input.getSku());
            req1.setWarehouseNo(input.getWarehouseNo());
            skuWarehouseNoQueryAreaReqList.add(req1);
        });
        req.setSkuWarehouseNoQueryAreaReqList(skuWarehouseNoQueryAreaReqList);
        return req;
    }

    public static List<AreaWarehouseNoSkuDTO> toAreaWarehouseNoSkuDTOList(List<AreaWarehouseNoSkuResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        List<AreaWarehouseNoSkuDTO> areaWarehouseNoSkuDTOS = new ArrayList<>();
        data.forEach(item -> {
            AreaWarehouseNoSkuDTO areaWarehouseNoSkuDTO = new AreaWarehouseNoSkuDTO();
            areaWarehouseNoSkuDTO.setSku(item.getSku());
            areaWarehouseNoSkuDTO.setWarehouseNo(item.getWarehouseNo());
            areaWarehouseNoSkuDTO.setAreaNos(item.getAreaNos());
            areaWarehouseNoSkuDTOS.add(areaWarehouseNoSkuDTO);
        });
        return areaWarehouseNoSkuDTOS;
    }
}
