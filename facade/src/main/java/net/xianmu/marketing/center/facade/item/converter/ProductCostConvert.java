package net.xianmu.marketing.center.facade.item.converter;

import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.inventory.client.productcost.dto.req.ProductCostBatchQueryV2Req;
import net.xianmu.marketing.center.facade.item.dto.ProductCostQueryDto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/2/21 17:27
 */
public class ProductCostConvert {


    private ProductCostConvert() {
        // 无需实现
    }

    public static List<ProductCostBatchQueryV2Req.WarehouseAndSkuQuery> toWarehouseAndSkuQueryList(List<ProductCostQueryDto> productCostQueryDtoList) {

        if (productCostQueryDtoList == null) {
            return Collections.emptyList();
        }
        List<ProductCostBatchQueryV2Req.WarehouseAndSkuQuery> warehouseAndSkuQueryList = new ArrayList<>();
        for (ProductCostQueryDto productCostQueryDto : productCostQueryDtoList) {
            warehouseAndSkuQueryList.add(toWarehouseAndSkuQuery(productCostQueryDto));
        }
        return warehouseAndSkuQueryList;
    }



    public static ProductCostBatchQueryV2Req.WarehouseAndSkuQuery toWarehouseAndSkuQuery(ProductCostQueryDto productCostQueryDto) {
        if (productCostQueryDto == null) {
            return null;
        }
        ProductCostBatchQueryV2Req.WarehouseAndSkuQuery warehouseAndSkuQuery = new ProductCostBatchQueryV2Req.WarehouseAndSkuQuery();
        warehouseAndSkuQuery.setWarehouseNo(productCostQueryDto.getWarehouseNo());
        warehouseAndSkuQuery.setSku(productCostQueryDto.getSku());
        return warehouseAndSkuQuery;
    }


    public static List<ProductCostQueryDto> toProductCostQueryDtoList(List<WarehouseBySkuAreaNoResp> warehouseBySkuAreaNoRespList) {
        if (warehouseBySkuAreaNoRespList == null) {
            return Collections.emptyList();
        }
        List<ProductCostQueryDto> productCostQueryDtoList = new ArrayList<>();
        for (WarehouseBySkuAreaNoResp warehouseBySkuAreaNoResp : warehouseBySkuAreaNoRespList) {
            productCostQueryDtoList.add(toProductCostQueryDto(warehouseBySkuAreaNoResp));
        }
        return productCostQueryDtoList;
    }

    public static ProductCostQueryDto toProductCostQueryDto(WarehouseBySkuAreaNoResp warehouseBySkuAreaNoResp) {
        if (warehouseBySkuAreaNoResp == null) {
            return null;
        }
        ProductCostQueryDto productCostQueryDto = new ProductCostQueryDto();
        productCostQueryDto.setWarehouseNo(warehouseBySkuAreaNoResp.getWarehouseNo());
        productCostQueryDto.setSku(warehouseBySkuAreaNoResp.getSku());
        productCostQueryDto.setAreaNo(warehouseBySkuAreaNoResp.getAreaNo());
// Not mapped FROM fields:
// warehouseName
        return productCostQueryDto;
    }

    public static List<WarehouseBySkuAreaNoResp> toWarehouseBySkuAreaNoRespList(List<ProductCostQueryDto> productCostQueryDtoList) {
        if (productCostQueryDtoList == null) {
            return Collections.emptyList();
        }
        List<WarehouseBySkuAreaNoResp> warehouseBySkuAreaNoRespList = new ArrayList<>();
        for (ProductCostQueryDto productCostQueryDto : productCostQueryDtoList) {
            warehouseBySkuAreaNoRespList.add(toWarehouseBySkuAreaNoResp(productCostQueryDto));
        }
        return warehouseBySkuAreaNoRespList;
    }

    public static WarehouseBySkuAreaNoResp toWarehouseBySkuAreaNoResp(ProductCostQueryDto productCostQueryDto) {
        if (productCostQueryDto == null) {
            return null;
        }
        WarehouseBySkuAreaNoResp warehouseBySkuAreaNoResp = new WarehouseBySkuAreaNoResp();
        warehouseBySkuAreaNoResp.setWarehouseNo(productCostQueryDto.getWarehouseNo());
        warehouseBySkuAreaNoResp.setSku(productCostQueryDto.getSku());
        warehouseBySkuAreaNoResp.setAreaNo(productCostQueryDto.getAreaNo());
// Not mapped TO fields:
// warehouseName
        return warehouseBySkuAreaNoResp;
    }
}
