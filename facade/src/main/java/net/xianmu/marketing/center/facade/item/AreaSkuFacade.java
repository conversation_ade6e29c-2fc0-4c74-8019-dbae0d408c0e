package net.xianmu.marketing.center.facade.item;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.areasku.AreaSkuQueryProvider;
import net.summerfarm.client.req.areasku.AreaSkuQueryReq;
import net.summerfarm.client.resp.areasku.AreaSkuResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.facade.item.converter.AreaSkuConvert;
import net.xianmu.marketing.center.facade.item.dto.AreaSkuDTO;
import net.xianmu.marketing.center.facade.item.input.AreaSkuQueryInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025/6/11 17:05
 * @PackageName:net.xianmu.marketing.center.facade.item
 * @ClassName: AreaSkuFacade
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@Component
public class AreaSkuFacade {

    @DubboReference
    private AreaSkuQueryProvider areaSkuQueryProvider;

    public List<AreaSkuDTO> querySkuAreaPrice(List<AreaSkuQueryInput> inputs) {
        if (CollectionUtils.isEmpty(inputs)) {
            return Collections.emptyList();
        }
        List<AreaSkuQueryReq> reqList = AreaSkuConvert.toAreaSkuQueryReqs(inputs);
        DubboResponse<List<AreaSkuResp>> listDubboResponse = areaSkuQueryProvider.queryAreaSkuBySkuAndAreaNoList(reqList);
        List<AreaSkuDTO> areaSkuDTOS = AreaSkuConvert.toAreaSkuDTOList(listDubboResponse.getData());
        return areaSkuDTOS;
    }
}
