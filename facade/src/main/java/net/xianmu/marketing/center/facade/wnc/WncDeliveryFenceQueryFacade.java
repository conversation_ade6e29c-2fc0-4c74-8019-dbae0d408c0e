package net.xianmu.marketing.center.facade.wnc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.DeliveryFenceQueryReq;
import net.summerfarm.wnc.client.req.fence.AreaQueryWarehouseNoSkuReq;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.fence.AreaWarehouseNoSkuResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.common.constant.CommonRedisKey;
import net.xianmu.marketing.center.common.util.RedisCacheUtil;
import net.xianmu.marketing.center.facade.wnc.converter.WncConverter;
import net.xianmu.marketing.center.facade.wnc.dto.AreaWarehouseNoSkuDTO;
import net.xianmu.marketing.center.facade.wnc.dto.DeliveryFenceDTO;
import net.xianmu.marketing.center.facade.wnc.input.DeliveryFenceQueryInput;
import net.xianmu.marketing.center.facade.wnc.input.SkuWarehouseNoQueryAreaInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName WncDeliveryFenceQueryFacade
 * @Description
 * <AUTHOR>
 * @Date 15:14 2024/4/9
 * @Version 1.0
 **/
@Slf4j
@Component
public class WncDeliveryFenceQueryFacade {

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    /***
     * @author: lzh
     * @description: 根据市区查询区域编码
     * @date: 2024/4/9 15:22
     * @param: [input]
     * @return: net.xianmu.marketing.center.facade.wnc.dto.DeliveryFenceDTO
     **/
    public DeliveryFenceDTO queryDeliveryFence(DeliveryFenceQueryInput input) {
        log.info("WncDeliveryFenceQueryFacade[]queryDeliveryFence[]input:{}", JSON.toJSONString(input));
        if (input == null) {
            return null;
        }
        DeliveryFenceQueryReq queryReq = WncConverter.toDeliveryFenceQueryReq(input);
        DubboResponse<DeliveryFenceResp> dubboResponse = deliveryFenceQueryProvider.queryDeliveryFence(queryReq);
        if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
            log.error("WncDeliveryFenceQueryFacade[]queryDeliveryFence[]queryDeliveryFence[]error!");
            return null;
        }
        DeliveryFenceDTO deliveryFenceDTO = WncConverter.toDeliveryFenceDTO(dubboResponse.getData());
        log.info("WncDeliveryFenceQueryFacade[]queryDeliveryFence[]deliveryFenceDTO:{}", JSON.toJSONString(deliveryFenceDTO));
        return deliveryFenceDTO;
    }

    /**
     * 根据市区查询区域编码 缓存版
     * @param input 入参
     * @return 运营编号
     */
    public Integer queryAreaNoForCache(DeliveryFenceQueryInput input) {
        log.info("WncDeliveryFenceQueryFacade[]queryAreaNoForCache[] input:{}",JSONObject.toJSONString(input));
        DeliveryFenceDTO deliveryFenceDTO = redisCacheUtil.getCacheObjectValue(
                CommonRedisKey.Cache.QUERY_DELIVERY_FENCE + JSONObject.toJSONString(input),
                10,
                () -> queryDeliveryFence(input),
                DeliveryFenceDTO.class);

        return deliveryFenceDTO == null ? null :deliveryFenceDTO.getAreaNo();
    }

    /***
     * @author: lzh
     * @description: 根据sku+warehouse查询区域编码
     * @date: 2024/4/9 15:22
     * @param: [input]
     * @return: net.xianmu.marketing.center.facade.wnc.dto.AreaWarehouseNoSkuDTO
     **/
    public List<AreaWarehouseNoSkuDTO> queryAreaByListWarehouseAndSku(List<SkuWarehouseNoQueryAreaInput> inputs) {
        if (CollectionUtils.isEmpty(inputs)) {
            return Collections.emptyList();
        }
        AreaQueryWarehouseNoSkuReq req = WncConverter.toAreaQueryWarehouseNoSkuReq(inputs);
        DubboResponse<List<AreaWarehouseNoSkuResp>> dubboResponse = deliveryFenceQueryProvider.queryAreaByListWarehouseAndSku(req);
        if (Objects.isNull(dubboResponse) || !DubboResponse.SUCCESS_STATUS.equals(dubboResponse.getStatus())){
            log.error("WncDeliveryFenceQueryFacade[]queryAreaByListWarehouseAndSku[]error!");
            return Collections.emptyList();
        }
        return WncConverter.toAreaWarehouseNoSkuDTOList(dubboResponse.getData());
    }
}
