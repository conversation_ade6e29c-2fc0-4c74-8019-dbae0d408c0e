package net.xianmu.marketing.center.facade.wnc.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/11 16:22
 * @PackageName:net.xianmu.marketing.center.facade.wnc.dto
 * @ClassName: WarehouseBySkuAreaNoDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class WarehouseBySkuAreaNoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer  warehouseNo;

    /**
     * 库存仓名称
     */
    private String warehouseName;

    /**
     * sku
     */
    private String sku;

    /**
     * 区域编号
     */
    private Integer areaNo;
}
