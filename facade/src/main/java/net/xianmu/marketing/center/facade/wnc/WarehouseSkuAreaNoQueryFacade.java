package net.xianmu.marketing.center.facade.wnc;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseSkuAreaNoQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.facade.wnc.converter.WncConverter;
import net.xianmu.marketing.center.facade.wnc.dto.WarehouseBySkuAreaNoDTO;
import net.xianmu.marketing.center.facade.wnc.input.WarehouseBySkuAreaNoDataInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description
 * @date 2023/6/30 15:22:39
 */
@Service
@Slf4j
public class WarehouseSkuAreaNoQueryFacade {

    @DubboReference
    private WarehouseSkuAreaNoQueryProvider warehouseSkuAreaNoQueryProvider;

    public List<WarehouseBySkuAreaNoDTO> queryBySkuAreNo(List<WarehouseBySkuAreaNoDataInput> inputs) {
        WarehouseBySkuAreaNoQueryReq req = WncConverter.toWarehouseBySkuAreaNoQueryReq(inputs);
        if (req == null) {
            return Collections.emptyList();
        }
        DubboResponse<List<WarehouseBySkuAreaNoResp>> response = warehouseSkuAreaNoQueryProvider.queryBySkuAreNo(req);
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())) {
            log.error("WarehouseSkuAreaNoQueryFacade[]queryBySkuAreNo[]error cause:{}", JSON.toJSONString(response));
            throw new BizException(response.getMsg());
        }
        List<WarehouseBySkuAreaNoDTO> warehouseBySkuAreaNoDTOS = WncConverter.toWarehouseBySkuAreaNoDTOs(response.getData());
        return warehouseBySkuAreaNoDTOS;
    }
}
