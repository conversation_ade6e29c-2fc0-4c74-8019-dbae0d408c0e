package net.xianmu.marketing.center.common.constant;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PropertiesUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName Conf
 * @Description
 * <AUTHOR>
 * @Date 13:47 2024/2/20
 * @Version 1.0
 **/
@Slf4j
public class Conf {

    //<EMAIL> 公众号
    public static String APP_Id ;
    public static String APP_Secret ;
    //<EMAIL> 小程序
    public static String MP_APP_ID;
    public static String MP_APP_SECRET;

    static {
        // 测试微信开发者ID
        APP_Id = PropertiesUtils.getProperty("wechat.app.id");
        APP_Secret = PropertiesUtils.getProperty("wechat.app.secret");
        MP_APP_ID = PropertiesUtils.getProperty("wechat.mp-app.id");
        MP_APP_SECRET = PropertiesUtils.getProperty("wechat.mp-app.secret");
        if (StringUtils.isBlank(APP_Id) || StringUtils.isBlank(APP_Secret) || StringUtils.isBlank(MP_APP_ID) || StringUtils.isBlank(MP_APP_SECRET)) {
            log.error("--------------------微信配置获取异常,请添加配置文件！--------------------");
        }
    }

    /**
     * 优惠券回调消息地址
     */
    public static String COUPON_MSG_URL = Global.DOMAIN_NAME + "/home.html#/loading?coupon=coupon";

    /**
     * 商城域名
     */
    public static String DOMAIN_NAME = "https://h5.summerfarm.net";

    /**
     * 特价活动详情
     */
    public static String ACTIVITY_DETAIL_URL = "/summerfarm-fe/activity/config/detail?id=";
}
