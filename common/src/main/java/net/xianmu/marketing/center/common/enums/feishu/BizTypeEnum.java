package net.xianmu.marketing.center.common.enums.feishu;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/4 16:09
 */
public enum BizTypeEnum {
    /**
     * 客户倒闭审批
     */
    CUSTOMER_FAIL(1, "客户倒闭审批"),
    COUPON_AUDIT(2, "卡券发起审批"),
    DMS_ACCOUNT_AUDIT(3, "DMS账号申请审批"),
    STOCKDAMAGE_AUDUT(4, "货损审批"),
    STOCKTAKING_AUDUT(5, "盘亏、盘盈审批"),
    MARKETING_AUDIT(6, "营销价审批"),
    SUPPLIER_AUDIT(7, "供应商审核审批"),
    SUPPLIER_CONTRACT_AUDIT(8, "供应商合同审核审批"),
    SRM_MODIFY_OFFER_DETAIL_AUDIT(9, "供应商修改生效中的报价审批"),
    MERCHANT_SITUATION(10, "客情申请审批"),
    PURCHASE_STATEMENT_APPROVAL_AUDIT(11, "采购对账单审批"),
    FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_AUDIT(12, "采购对账单付款单审批"),
    PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT(13, "采购预付单审批"),
    FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_AUDIT(14, "采购预付付款单审批"),
    BMS_PAYMENT_ORDER_APPROVAL_AUDIT(15, "BMS付款单审批"),
    COST_ADJUSTMENT_AUDIT(16, "结算单费用调整审批"),
    PATH_COST_ADJUSTMENT_AUDIT(17, "结算单路线费用调整审批"),
    RECONCILIATION_COST_ADJUSTMENT_AUDIT(18, "对账单费用调整审批"),
    BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT(19, "结算单对账审批"),
    BMS_RECONCILIATION_PAYMENT_AUDIT(20, "对账单打款审批"),
    SECURITY_STOCK_AUDIT(21,"安全库存审批"),
    MERCHANT_CANCEL_AUDIT(22,"门店注销审批"),
    FOLLOW_UP_UPDATE_POI(24,"拜访记录更新POI"),
    COUPON_AUDIT_NOT_NEED_PRINCIPAL(25,"卡劵人工发放不需要负责人审批"),
    COUPON_AUDIT_NEED_PRINCIPAL(26,"卡劵人工发放需要负责人审批"),


    PAYEE_ADJUSTMENT_AUDIT(70, "承运商调整审批"),
    SPECIAL_PRICE_APPLICATION_BY_SUPPLIER(99, "供应商特价活动申请审批"),

    ;

    private Integer id;
    private String value;

    BizTypeEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
