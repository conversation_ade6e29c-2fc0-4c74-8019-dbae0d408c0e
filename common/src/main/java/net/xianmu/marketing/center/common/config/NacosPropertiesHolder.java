package net.xianmu.marketing.center.common.config;

import com.alibaba.fastjson.JSON;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * Nacos动态配置；
 */
@Data
@Slf4j
@Component
@NacosConfigurationProperties(dataId = "${spring.application.name}",
    type = ConfigType.PROPERTIES,
    autoRefreshed = true,
    ignoreInvalidFields = true)
public class NacosPropertiesHolder implements InitializingBean {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    /**
     * 发送审批流默认提交人
     */
    @Value(value = "${defaultFeiShuSubmitter:11989}")
    private Integer defaultFeiShuSubmitter;

    @Override
    public void afterPropertiesSet() {
        log.info("Nacos监听表配置项, application name:{}, :{}", applicationName, JSON.toJSONString(this));
    }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }
}
