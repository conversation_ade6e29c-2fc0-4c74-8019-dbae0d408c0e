package net.xianmu.marketing.center.common.enums.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @create: 2022/12/19
 */
@Getter
@AllArgsConstructor
public enum ActivityTagEnum {

    SLOW_SALE_PROMOTION(0, "滞销促销"),

    NEAR_EXPIRED_CLEARANCE(1, "临保清仓"),

    NEW_PRODUCT_PROMOTION(2, "新品推广"),

    USER_RECALL(3, "用户召回"),

    POTENTIAL_PROMOTION(4, "潜力品推广"),

    LIVE_PROMOTION(5, "直播推广"),

    TEMPORARY_INSURANCE_RISK(6, "临保风险品");

    private int code;

    private String value;


}
