package net.xianmu.marketing.center.common.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DingdingFormBO {

    /**
     * 钉钉表单名称-与钉钉OA工作台中定义的流程模版表单名称一致-必填
     */
    private String formName;

    /**
     * 钉钉表单名称输入值-必填
     */
    private String formValue;

    /**
     * 扩展值-非必须（官方文档有，在应用内预留）
     */
    private String extValue;


    public DingdingFormBO(String name, String value){
        this.formName = name;
        this.formValue = value;
    }
}
