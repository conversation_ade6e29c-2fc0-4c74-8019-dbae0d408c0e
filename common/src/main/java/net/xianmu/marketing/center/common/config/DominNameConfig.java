package net.xianmu.marketing.center.common.config;

import net.xianmu.marketing.center.common.constant.Conf;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @Date 2025/5/23 17:25
 * @PackageName:net.summerfarm.manage.common.config
 * @ClassName: DominNameConfig
 * @Description: TODO
 * @Version 1.0
 */
@Configuration
public class DominNameConfig {

    @Value("${xianmu.mall.domain:}")
    private String domainName;

    @PostConstruct
    public void init() {
        Conf.DOMAIN_NAME = domainName;
    }
}
