package net.xianmu.marketing.center.common.enums.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/12 10:55
 * @PackageName:net.xianmu.marketing.center.common.enums.activity
 * @ClassName: ActivityAuditStatusEunm
 * @Description: TODO
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum ActivityAuditStatusEnum {

    PENDING_REVIEW(0, "待审核"),
    APPROVED(1, "审核通过"),
    REJECTED(2, "审核拒绝");

    private int code;

    private String value;
}
