package net.xianmu.marketing.center.common.input;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import net.xianmu.marketing.center.common.enums.feishu.ProcessInstanceBizTypeEnum;

import java.util.List;

/**
 * 钉钉审批工作流-流程实例参数
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-21
 */
@Data
@Builder
@AllArgsConstructor
public class ProcessInstanceCreateBO {

    /**
     * 发起审批的业务
     * @see ProcessInstanceBizTypeEnum
     */
    private ProcessInstanceBizTypeEnum bizTypeEnum;

    /**
     * 发起审批的业务数据id
     */
    private Long bizId;

    /**
     * 发起审批的系统用户id
     */
    private Integer adminId;

    /**
     * 钉钉审批流表单信息-必传
     */
    private List<DingdingFormBO> dingdingForms;

    /**
     * 审批实例发起人的userid-钉钉中的用户id
     */
    private String originatorUserId;

    /**
     * 发起人所在的部门，如果发起人属于根部门，传-1。
     */
    private Long deptId;

    /**
     * 审批人userid列表，最大列表长度20。
     * 多个审批人用逗号分隔，按传入的顺序依次审批。
     */
    private String approvers;

    /**
     * 抄送人 userId。
     * Array of String
     */
    private String ccList;

    /**
     * 抄送时间点
     */
    private String ccPosition;





    public ProcessInstanceCreateBO() {
    }

    public ProcessInstanceCreateBO(ProcessInstanceBizTypeEnum bizTypeEnum, Long bizId, Integer adminId, List<DingdingFormBO> dingdingForms) {
        this.bizTypeEnum = bizTypeEnum;
        this.bizId = bizId;
        this.adminId = adminId;
        this.dingdingForms = dingdingForms;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
