package net.xianmu.marketing.center.common.constant;

import lombok.extern.slf4j.Slf4j;

/**
 * 钉钉key-value
 * <AUTHOR>
 * @since 2022-04-12
 */
@Slf4j
public class DingdingConstantKey {

    /**
     * 钉钉客户倒闭审批模板code
     */
    public static final String CUSTOMER_FAIL_CODE = "CUSTOMER_FAIL_CODE";
    /**
     * 卡券发放审批模板
     */
    public static final String COUPON_AUDIT_CODE = "COUPON_AUDIT_CODE";
    /**
     * DMS账号申请模板
     */
    public static final String DMS_ACCOUNT_AUDIT_CODE = "DMS_ACCOUNT_AUDIT_CODE";
    /**
     * 货损审批模板code
     */
    public static final String STOCKDAMAGE_AUDUT_CODE = "STOCKDAMAGE_AUDUT_CODE";
    /**
     * 盘盈&盘亏模板code
     */
    public static final String STOCKTAKING_AUDUT_CODE = "STOCKTAKING_AUDUT_CODE";

    /**
     * 营销审批
     */
    public static final String MARKETING_AUDIT_CODE = "MARKETING_AUDIT_CODE";

    /**
     * 供应商审核审批
     */
    public static final String SUPPLIER_AUDIT_CODE = "SUPPLIER_AUDIT_CODE";

    /**
     * 供应商合同审批
     */
    public static final String SUPPLIER_CONTRACT_AUDIT_CODE = "SUPPLIER_CONTRACT_AUDIT_CODE";

    /**
     * 供应商修改生效中的报价详情审批
     */
    public static final String SRM_MODIFY_OFFER_DETAIL_CODE = "SRM_MODIFY_OFFER_DETAIL_CODE";
    /**
     * 客情券审批 - 客情和品类券
     */
    public static final String MERCHANT_SITUATION = "MERCHANT_SITUATION";

    /**
     * 采购对账单审批
     */
    public static final String PURCHASE_STATEMENT_APPROVAL_CODE = "PURCHASE_STATEMENT_APPROVAL_CODE";

    /**
     * 采购对账单 付款单审批
     */
    public static final String FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_CODE = "FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_CODE";

    /**
     * 采购预付单审批
     */
    public static final String PURCHASE_ADVANCED_ORDER_APPROVAL_CODE = "PURCHASE_ADVANCED_ORDER_APPROVAL_CODE";

    /**
     * 预付单 付款单审批
     */
    public static final String FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_CODE = "FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_CODE";
    /**
     * BMS 付款单审批
     */
    public static final String BMS_PAYMENT_ORDER_APPROVAL_CODE = "BMS_PAYMENT_ORDER_APPROVAL_CODE";
    /**
     * 费用调整审批
     */
    public static final String COST_ADJUSTMENT_AUDIT_CODE = "COST_ADJUSTMENT_AUDIT_CODE";

    public static final String PAYEE_AUDIT_CODE = "PAYEE_AUDIT_CODE";


    /**
     * 对账打款单审批
     */
    public static final String BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT_CODE = "BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT_CODE";
    /**
     * 打款单申请打款审批
     */
    public static final String BMS_RECONCILIATION_PAYMENT_AUDIT_CODE = "BMS_RECONCILIATION_PAYMENT_AUDIT_CODE";
    /**
     * 安全库存审批
     */
    public static final String SECURITY_STOCK_AUDIT = "SECURITY_STOCK_AUDIT";
    /**
     * 安全库存审批
     */
    public static final String MERCHANT_CANCEL_AUDIT = "MERCHANT_CANCEL_CODE";
    /**
     * 拜访计划修改poi
     */
    public static final String FOLLOW_UP_UPDATE_POI = "FOLLOW_UP_UPDATE_POI";

    /**
     * 卡劵人工发放不需要负责人审批
     */
    public static final String COUPON_AUDIT_NOT_NEED_PRINCIPAL = "COUPON_AUDIT_NOT_NEED_PRINCIPAL";

    /**
     * 卡劵人工发放需要负责人审批
     */
    public static final String COUPON_AUDIT_NEED_PRINCIPAL = "COUPON_AUDIT_NEED_PRINCIPAL";

    /**
     * 供应商特价活动申请审批
     */
    public static final String SPECIAL_PRICE_APPLICATION_BY_SUPPLIER = "SPECIAL_PRICE_APPLICATION_BY_SUPPLIER";
}
