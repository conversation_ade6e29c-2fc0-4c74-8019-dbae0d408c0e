package net.xianmu.marketing.center.common.enums.feishu;

import lombok.Getter;
import net.xianmu.marketing.center.common.constant.DingdingConstantKey;

import java.util.Arrays;

/**
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-22
 */
@Getter
public enum ProcessInstanceBizTypeEnum {
    /**
     * 客户倒闭审批,bizType = 1
     */
    CUSTOMER_FAIL(BizTypeEnum.CUSTOMER_FAIL.getId(), DingdingConstantKey.CUSTOMER_FAIL_CODE, BizTypeEnum.CUSTOMER_FAIL.getValue()),
    COUPON_AUDIT(BizTypeEnum.COUPON_AUDIT.getId(), DingdingConstantKey.COUPON_AUDIT_CODE, BizTypeEnum.COUPON_AUDIT.getValue()),
    DMS_ACCOUNT_AUDIT(BizTypeEnum.DMS_ACCOUNT_AUDIT.getId(), DingdingConstantKey.DMS_ACCOUNT_AUDIT_CODE, BizTypeEnum.DMS_ACCOUNT_AUDIT.getValue()),
    /**
     * 货损审批.
     */
    STOCKDAMAGE_AUDUT(BizTypeEnum.STOCKDAMAGE_AUDUT.getId(), DingdingConstantKey.STOCKDAMAGE_AUDUT_CODE, BizTypeEnum.STOCKDAMAGE_AUDUT.getValue()),
    /**
     * 盘盈&盘亏
     */
    STOCKTAKING_AUDUT(BizTypeEnum.STOCKTAKING_AUDUT.getId(), DingdingConstantKey.STOCKTAKING_AUDUT_CODE, BizTypeEnum.STOCKTAKING_AUDUT.getValue()),
    /**
     * 营销价审批
     */
    MARKETING_AUDIT_DEV(BizTypeEnum.MARKETING_AUDIT.getId(), DingdingConstantKey.MARKETING_AUDIT_CODE, BizTypeEnum.MARKETING_AUDIT.getValue()),
    MARKETING_AUDIT_PRO(BizTypeEnum.MARKETING_AUDIT.getId(), DingdingConstantKey.MARKETING_AUDIT_CODE, BizTypeEnum.MARKETING_AUDIT.getValue()),
    /**
     * 供应商审核审批
     */
    SUPPLIER_AUDIT(BizTypeEnum.SUPPLIER_AUDIT.getId(), DingdingConstantKey.SUPPLIER_AUDIT_CODE, BizTypeEnum.SUPPLIER_AUDIT.getValue()),
    /**
     * 供应商合同审核
     */
    SUPPLIER_CONTRACT_AUDIT(BizTypeEnum.SUPPLIER_CONTRACT_AUDIT.getId(), DingdingConstantKey.SUPPLIER_CONTRACT_AUDIT_CODE, BizTypeEnum.SUPPLIER_CONTRACT_AUDIT.getValue()),
    /**
     * 供应商合同审核
     */
    SRM_MODIFY_OFFER_DETAIL_AUDIT(BizTypeEnum.SRM_MODIFY_OFFER_DETAIL_AUDIT.getId(), DingdingConstantKey.SRM_MODIFY_OFFER_DETAIL_CODE, BizTypeEnum.SRM_MODIFY_OFFER_DETAIL_AUDIT.getValue()),
    /**
     * 客情券审批
     */
    MERCHANT_SITUATION(BizTypeEnum.MERCHANT_SITUATION.getId(), DingdingConstantKey.MERCHANT_SITUATION, BizTypeEnum.MERCHANT_SITUATION.getValue()),

    /**
     * 采购对账单审批
     */
    PURCHASE_STATEMENT_APPROVAL_AUDIT(BizTypeEnum.PURCHASE_STATEMENT_APPROVAL_AUDIT.getId(), DingdingConstantKey.PURCHASE_STATEMENT_APPROVAL_CODE, BizTypeEnum.PURCHASE_STATEMENT_APPROVAL_AUDIT.getValue()),

    /**
     * 采购对账单 付款单审批
     */
    FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_AUDIT(BizTypeEnum.FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_AUDIT.getId(), DingdingConstantKey.FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_CODE, BizTypeEnum.FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_AUDIT.getValue()),

    /**
     * 采购预付审批
     */
    PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT(BizTypeEnum.PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT.getId(), DingdingConstantKey.PURCHASE_ADVANCED_ORDER_APPROVAL_CODE, BizTypeEnum.PURCHASE_ADVANCED_ORDER_APPROVAL_AUDIT.getValue()),

    /**
     * 采购预付单 付款单审批
     */
    FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_AUDIT(BizTypeEnum.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_AUDIT.getId(), DingdingConstantKey.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_CODE, BizTypeEnum.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_AUDIT.getValue()),
    /**
     * BMS 打款单审批
     */
    BMS_PAYMENT_ORDER_APPROVAL_AUDIT(BizTypeEnum.BMS_PAYMENT_ORDER_APPROVAL_AUDIT.getId(), DingdingConstantKey.BMS_PAYMENT_ORDER_APPROVAL_CODE, BizTypeEnum.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_AUDIT.getValue()),

    /**
     * 结算单费用调整审批
     */
    COST_ADJUSTMENT_AUDIT(BizTypeEnum.COST_ADJUSTMENT_AUDIT.getId(), DingdingConstantKey.COST_ADJUSTMENT_AUDIT_CODE, BizTypeEnum.COST_ADJUSTMENT_AUDIT.getValue()),

    /**
     * 结算单路线费用调整审批
     */
    PATH_COST_ADJUSTMENT_AUDIT(BizTypeEnum.PATH_COST_ADJUSTMENT_AUDIT.getId(), DingdingConstantKey.COST_ADJUSTMENT_AUDIT_CODE, BizTypeEnum.PATH_COST_ADJUSTMENT_AUDIT.getValue()),

    /**
     * 对账单费用调整审批
     */
    RECONCILIATION_COST_ADJUSTMENT_AUDIT(BizTypeEnum.RECONCILIATION_COST_ADJUSTMENT_AUDIT.getId(), DingdingConstantKey.COST_ADJUSTMENT_AUDIT_CODE, BizTypeEnum.RECONCILIATION_COST_ADJUSTMENT_AUDIT.getValue()),

    /**
     * 对账打款审批
     */
    BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT(BizTypeEnum.BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT.getId(), DingdingConstantKey.BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT_CODE, BizTypeEnum.BMS_SETTLE_ACCOUNT_RECONCILIATION_AUDIT.getValue()),

    /**
     * 打款单打款审批
     */
    BMS_RECONCILIATION_PAYMENT_AUDIT(BizTypeEnum.BMS_RECONCILIATION_PAYMENT_AUDIT.getId(), DingdingConstantKey.BMS_RECONCILIATION_PAYMENT_AUDIT_CODE, BizTypeEnum.BMS_RECONCILIATION_PAYMENT_AUDIT.getValue()),

    /**
     * 安全库存变动审批
     */
    SECURITY_STOCK_AUDIT(BizTypeEnum.SECURITY_STOCK_AUDIT.getId(), DingdingConstantKey.SECURITY_STOCK_AUDIT, BizTypeEnum.SECURITY_STOCK_AUDIT.getValue()),

    /**
     * 门店注销审批
     */
    MERCHANT_CANCEL_AUDIT(BizTypeEnum.MERCHANT_CANCEL_AUDIT.getId(), DingdingConstantKey.MERCHANT_CANCEL_AUDIT, BizTypeEnum.MERCHANT_CANCEL_AUDIT.getValue()),
    FOLLOW_UP_UPDATE_POI(BizTypeEnum.FOLLOW_UP_UPDATE_POI.getId(), DingdingConstantKey.FOLLOW_UP_UPDATE_POI, BizTypeEnum.FOLLOW_UP_UPDATE_POI.getValue()),



    /**
     * 承运商调整
     */
    PAYEE_ADJUSTMENT_AUDIT(BizTypeEnum.PAYEE_ADJUSTMENT_AUDIT.getId(), DingdingConstantKey.PAYEE_AUDIT_CODE, BizTypeEnum.PAYEE_ADJUSTMENT_AUDIT.getValue()),

    /**
     * 卡劵人工发放不需要负责人审批
     */
    COUPON_AUDIT_NOT_NEED_PRINCIPAL(BizTypeEnum.COUPON_AUDIT_NOT_NEED_PRINCIPAL.getId(), DingdingConstantKey.COUPON_AUDIT_NOT_NEED_PRINCIPAL, BizTypeEnum.COUPON_AUDIT_NOT_NEED_PRINCIPAL.getValue()),

    /**
     * 卡劵人工发放需要负责人审批
     */
    COUPON_AUDIT_NEED_PRINCIPAL(BizTypeEnum.COUPON_AUDIT_NEED_PRINCIPAL.getId(), DingdingConstantKey.COUPON_AUDIT_NEED_PRINCIPAL, BizTypeEnum.COUPON_AUDIT_NEED_PRINCIPAL.getValue()),

    /**
     * 供应商特价活动申请审批
     */
    SPECIAL_PRICE_APPLICATION_BY_SUPPLIER(BizTypeEnum.SPECIAL_PRICE_APPLICATION_BY_SUPPLIER.getId(), DingdingConstantKey.SPECIAL_PRICE_APPLICATION_BY_SUPPLIER, BizTypeEnum.SPECIAL_PRICE_APPLICATION_BY_SUPPLIER.getValue()),
    ;
    /**
     * 业务类型
     */
    private final Integer bizType;

    /**
     * 钉钉审批模版编码
     */
    private final String processCode;

    /**
     * 描述
     */
    private final String description;

    ProcessInstanceBizTypeEnum(int bizType, String processCode, String description) {
        this.bizType = bizType;
        this.processCode = processCode;
        this.description = description;
    }

    public static ProcessInstanceBizTypeEnum convert(Integer param) {
        return Arrays.stream(ProcessInstanceBizTypeEnum.values())
                .filter(o -> o.getBizType().equals(param))
                .findFirst().get();
    }
}
