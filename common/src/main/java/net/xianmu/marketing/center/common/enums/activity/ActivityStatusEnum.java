package net.xianmu.marketing.center.common.enums.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <EMAIL>
 * @create: 2023/12/26
 */
@Getter
@AllArgsConstructor
public enum ActivityStatusEnum {

    PENDING_REVIEW(-1, "待审核"),
    NOT_VALID(0, "未生效"),
    EFFECTING(1, "已生效"),
    FINISHED(2, "已失效"),
    DELETE(3, "已删除"),
    REJECTED(100, "审核拒绝");

    private Integer code;

    private String value;

}
