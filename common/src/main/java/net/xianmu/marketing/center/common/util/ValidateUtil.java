package net.xianmu.marketing.center.common.util;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * @description 校验参数合法
 */
@Slf4j
public class ValidateUtil {

    private static Pattern pattern = Pattern.compile("^([0-9]{11})$");
    /**
     * 校验参数是否在数组中
     *
     * @param parameter
     * @param objectArr
     * @return
     */
    public static boolean isContains(Object parameter, Object[] objectArr) {
        if (null == objectArr || null == parameter) {
            return false;
        }
        for (Object obj : objectArr) {
            if (parameter.equals(obj)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验参数是否在map中
     *
     * @param reqDto map
     * @param keys   key
     * @return
     */
    public static boolean isContains(Map<String, String> reqDto, String... keys) {
        if (reqDto == null || reqDto.size() == 0) {
            return false;
        }
        if (keys.length == 0) {
            return false;
        }
       return Arrays.stream(keys).anyMatch(reqDto::containsKey);
    }


    /**
     * 参数校验
     *
     * @return
     */
    public static void paramValidate(Map<String, Object> reqDto, String... keys) {
        if (reqDto == null || reqDto.size() == 0) {
            log.error("请求参数为空!!");
            throw new ParamsException("请求参数为空!!");
        }
        if (keys.length == 0) {
            return;
        }
        for (String key : keys) {
            if (reqDto.get(key) == null) {
                log.error("请求参数:{}为空!!", key);
                throw new ParamsException(String.format("请求参数:%s为空!!", key));
            }
        }
    }


    /**
     * 参数校验
     *
     * @return
     */
    public static void paramValidate(Object object, String... keys) {
        if (object == null) {
            log.error("请求参数为空!!");
            throw new ParamsException("请求参数为空!!");
        }
        if (keys.length == 0) {
            return;
        }
        Class<?> clazz = object.getClass();
        for (String key : keys) {
            try {
                Field field = clazz.getDeclaredField(key);
                field.setAccessible(true);
                Object value = field.get(object);
                if (value == null) {
                    log.error("请求参数:{}为空!!", key);
                    throw new ParamsException(String.format("请求参数:%s为空!!", key));
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.error("参数检验异常", e);
            }
        }
    }


    public static String transNull(String string) {
        return string == null ? "" : string;
    }



    public static boolean isMobile(String mobiles) {
        if (StrUtil.isBlank(mobiles)) {
            return false;
        } else {
            return pattern.matcher(mobiles).matches();
        }
    }


    /**
     * 校验地址是否规范
     *
     * @param storeAddress
     * @return
     */
    public static boolean isAddress(String storeAddress) {
        return StrUtil.isNotBlank(storeAddress) && (storeAddress.length() <= 100);
    }

    /**
     * 校验门牌号是否规范
     *
     * @return
     */
    public static boolean isHouseNumber(String houseNumber) {
        return houseNumber.matches("[\\u4e00-\\u9fa5a-zA-Z0-9-]{1,50}");
    }

    /**
     * 校验门店名称是否规范
     *
     * @param storeName
     * @return
     */
    public static boolean isStoreName(String storeName) {
        return StrUtil.isNotBlank(storeName) && storeName.matches("[\\u4e00-\\u9fa5a-zA-Z0-9]{1,20}");
    }


    /**
     * 获取source中存在，target中不存在的数据
     *
     * @param source
     * @param target
     * @return
     */
    public static List<Long> getDifference(List<Long> source, List<Long> target) {
        if (CollUtil.isEmpty(source)) {
            return new ArrayList<>();
        }

        if (CollUtil.isEmpty(target)) {
            return source;
        }
        return source.stream()
                .filter(element -> !target.contains(element))
                .collect(Collectors.toList());
    }





    // 自定义多字段去重比较器
    public static <T> Predicate<T> distinctByKeys(Function<? super T, ? extends List<?>> keyExtractors) {
        Map<List<?>, Boolean> seen = new HashMap<>();
        return t -> {
            List<?> keys = keyExtractors.apply(t);
            return seen.putIfAbsent(keys, Boolean.TRUE) == null;
        };
    }

    //
    public static String toStringWithNull(Object o){
        return o == null ? "" : o.toString();
    }

}
