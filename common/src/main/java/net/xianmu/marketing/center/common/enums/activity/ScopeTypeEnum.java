package net.xianmu.marketing.center.common.enums.activity;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;

/**
 * @author: <EMAIL>
 * @create: 2023/12/26
 */
@Getter
@AllArgsConstructor
public enum ScopeTypeEnum {

    ALL(0, "全部"),
    MERCHANT_POOL(1, "人群包"),
    AREA(2, "运营城市"),
    LARGE_AREA(3, "运营大区"),
    WAREHOUSE(4, "库存仓");

    private int code;

    private String value;

    public static ScopeTypeEnum getByCode(Integer code) {
        for (ScopeTypeEnum typeEnum : ScopeTypeEnum.values()) {
            if (Objects.equals(code, typeEnum.code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static List<Integer> getCodeList(Integer systemOrigin) {
        if (Objects.equals(systemOrigin, SystemOriginEnum.SRM.getType())) {
            return Arrays.asList(WAREHOUSE.code);
        } else {
            return Arrays.asList(ALL.code, MERCHANT_POOL.code, AREA.code, LARGE_AREA.code);
        }
    }
}
