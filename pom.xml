<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>
    <groupId>net.xianmu</groupId>
    <artifactId>marketing-center</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-RELEASE</version>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>

        <app.version>1.0.0-RELEASE</app.version>
        <app.client.version>1.0.12-lzh-SNAPSHOT</app.client.version>

        <xianmu-common.version>1.1.7-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.9</xianmu-dubbo.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <xianmu-task.version>1.0.5</xianmu-task.version>
        <xianmu-oss.version>1.0.2</xianmu-oss.version>
        <xianmu-mq.version>1.2.0</xianmu-mq.version>
        <authentication-sdk.version>1.1.2</authentication-sdk.version>
        <authentication-client.version>1.1.25</authentication-client.version>
        <manage-client.version>1.0.45-RELEASE</manage-client.version>
        <message-client.version>1.3.1-RELEASE</message-client.version>
        <sf-mall-manage-client.version>1.2.5-lzh-SNAPSHOT</sf-mall-manage-client.version>
        <summerfarm-common.version>1.4.8</summerfarm-common.version>


        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.18.26</lombok.version>
        <starter.version>2.1.1</starter.version>
        <page.version>1.2.7</page.version>
        <mysql-connector.version>8.0.23</mysql-connector.version>
        <druid.version>1.1.20</druid.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <dubbo-registry-nacos.version>2.7.15</dubbo-registry-nacos.version>
        <schedulerx2.version>1.7.4</schedulerx2.version>
        <redisson.version>3.11.1</redisson.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <hibernate-validator.version>6.2.0.Final</hibernate-validator.version>
        <mapstruct.version>1.2.0.Final</mapstruct.version>
        <hutool-all.version>5.8.20</hutool-all.version>
        <gauva.version>28.2-jre</gauva.version>
        <shiro-core.version>1.2.5</shiro-core.version>
        <nacos-config.version>0.2.12</nacos-config.version>
        <mybatis.version>3.5.0</mybatis.version>
        <sentinel.version>1.0.1-RELEASE</sentinel.version>
        <wnc-client.version>1.4.1-RELEASE</wnc-client.version>
        <redis-support.version>1.2.1</redis-support.version>
        <mall-client.version>1.0.36-RELEASE</mall-client.version>
        <common-client.version>1.0.7-RELEASE</common-client.version>
        <inventory-client.verison>2.0.24-RELEASE</inventory-client.verison>
    </properties>

    <dependencyManagement>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependencies>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-common</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-facade</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-domain</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-application</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-infrastructure</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-starter</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-client</artifactId>
                <version>${app.client.version}</version>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-oss-support</artifactId>
                <version>${xianmu-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${xianmu-task.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-sdk</artifactId>
                <version>${authentication-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-client</artifactId>
                <version>${authentication-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.manage.client</groupId>
                <artifactId>manage-client</artifactId>
                <version>${manage-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>sf-mall-manage-client</artifactId>
                <version>${sf-mall-manage-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-common</artifactId>
                <version>${summerfarm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>message-client</artifactId>
                <version>${message-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-sentinel-support</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-client</artifactId>
                <version>${wnc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-redis-support</artifactId>
                <version>${redis-support.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-client</artifactId>
                <version>${mall-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>common-client</artifactId>
                <version>${common-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>summerfarm-inventory-client</artifactId>
                <version>${inventory-client.verison}</version>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->



            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <!-- 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${page.version}</version>
            </dependency>
            <!-- 数据库组件——mysql连接组件 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- alibaba开源数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 注册中心 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo-registry-nacos.version}</version>
            </dependency>
            <!-- rocket mq -->
<!--            <dependency>-->
<!--                <groupId>org.apache.rocketmq</groupId>-->
<!--                <artifactId>rocketmq-spring-boot-starter</artifactId>-->
<!--                <version>${rocketmq.starter.version}</version>-->
<!--            </dependency>-->
            <!-- alibaba json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!-- validation -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <!--mapstruct-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!-- hutool 工具包-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <!-- Google guava工具包-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${gauva.version}</version>
            </dependency>
            <dependency>
                <artifactId>shiro-core</artifactId>
                <groupId>org.apache.shiro</groupId>
                <version>${shiro-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
<!--                    <annotationProcessorPaths>-->
<!--                        &lt;!&ndash; MapStruct编译，注解处理器，根据注解自动生成Mapper的实现 &ndash;&gt;-->
<!--                        <path>-->
<!--                            <groupId>org.mapstruct</groupId>-->
<!--                            <artifactId>mapstruct-processor</artifactId>-->
<!--                            <version>${mapstruct.version}</version>-->
<!--                        </path>-->
<!--                    </annotationProcessorPaths>-->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>    

<modules>  <module>application</module>
    <module>domain</module>
    <module>infrastructure</module>
    <module>common</module>
    <module>facade</module>
    <module>starter</module>
  </modules>
</project>
